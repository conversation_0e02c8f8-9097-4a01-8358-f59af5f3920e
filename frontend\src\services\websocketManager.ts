/**
 * WebSocket Connection Manager
 * Handles robust WebSocket connections with automatic reconnection
 */

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
}

export interface WebSocketConfig {
  url: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
  connectionTimeout: number;
}

export class WebSocketManager {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isDestroyed = false;
  private messageQueue: WebSocketMessage[] = [];

  // Event handlers
  private onOpenHandlers: Array<() => void> = [];
  private onCloseHandlers: Array<(event: CloseEvent) => void> = [];
  private onErrorHandlers: Array<(error: Event) => void> = [];
  private onMessageHandlers: Array<(message: WebSocketMessage) => void> = [];

  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = {
      url: config.url || "ws://localhost:8000/ws",
      reconnectInterval: config.reconnectInterval || 5000, // Start with 5s to reduce spam
      maxReconnectAttempts: config.maxReconnectAttempts || 10,
      heartbeatInterval: config.heartbeatInterval || 30000,
      connectionTimeout: config.connectionTimeout || 10000,
      ...config,
    };
  }

  /**
   * Connect to WebSocket server
   */
  async connect(): Promise<void> {
    if (this.isDestroyed) {
      throw new Error("WebSocket manager has been destroyed");
    }

    if (
      this.isConnecting ||
      (this.ws && this.ws.readyState === WebSocket.CONNECTING)
    ) {
      return;
    }

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return;
    }

    this.isConnecting = true;
    console.log(`Connecting to WebSocket: ${this.config.url}`);

    try {
      this.ws = new WebSocket(this.config.url);

      // Set up event handlers
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);

      // Connection timeout
      const timeout = setTimeout(() => {
        if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
          console.warn("WebSocket connection timeout");
          this.ws.close();
        }
      }, this.config.connectionTimeout);

      // Wait for connection to open
      await new Promise<void>((resolve, reject) => {
        const onOpen = () => {
          clearTimeout(timeout);
          resolve();
        };

        const onError = (error: Event) => {
          clearTimeout(timeout);
          reject(error);
        };

        this.onOpenHandlers.push(onOpen);
        this.onErrorHandlers.push(onError);
      });
    } catch (error) {
      this.isConnecting = false;
      console.error("WebSocket connection failed:", error);
      throw error;
    }
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    this.clearTimers();

    if (this.ws) {
      this.ws.close(1000, "Manual disconnect");
      this.ws = null;
    }

    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  /**
   * Destroy the WebSocket manager
   */
  destroy(): void {
    this.isDestroyed = true;
    this.disconnect();
    this.clearEventHandlers();
  }

  /**
   * Send message to WebSocket server
   */
  send(message: Omit<WebSocketMessage, "timestamp">): boolean {
    const fullMessage: WebSocketMessage = {
      ...message,
      timestamp: Date.now(),
    };

    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(fullMessage));
        return true;
      } catch (error) {
        console.error("Failed to send WebSocket message:", error);
        this.messageQueue.push(fullMessage);
        return false;
      }
    } else {
      // Queue message for when connection is restored
      this.messageQueue.push(fullMessage);
      console.warn("WebSocket not connected, message queued");
      return false;
    }
  }

  /**
   * Get connection status
   */
  getStatus(): {
    connected: boolean;
    connecting: boolean;
    readyState: number | null;
    reconnectAttempts: number;
    queuedMessages: number;
  } {
    return {
      connected: this.ws?.readyState === WebSocket.OPEN,
      connecting: this.isConnecting,
      readyState: this.ws?.readyState || null,
      reconnectAttempts: this.reconnectAttempts,
      queuedMessages: this.messageQueue.length,
    };
  }

  /**
   * Event handler registration
   */
  onOpen(handler: () => void): void {
    this.onOpenHandlers.push(handler);
  }

  onClose(handler: (event: CloseEvent) => void): void {
    this.onCloseHandlers.push(handler);
  }

  onError(handler: (error: Event) => void): void {
    this.onErrorHandlers.push(handler);
  }

  onMessage(handler: (message: WebSocketMessage) => void): void {
    this.onMessageHandlers.push(handler);
  }

  /**
   * Handle WebSocket open event
   */
  private handleOpen(): void {
    console.log("WebSocket connected successfully");
    this.isConnecting = false;
    this.reconnectAttempts = 0;

    // Start heartbeat
    this.startHeartbeat();

    // Send queued messages
    this.flushMessageQueue();

    // Notify handlers
    this.onOpenHandlers.forEach((handler) => {
      try {
        handler();
      } catch (error) {
        console.error("Error in WebSocket open handler:", error);
      }
    });
  }

  /**
   * Handle WebSocket close event
   */
  private handleClose(event: CloseEvent): void {
    console.log("WebSocket disconnected:", event.code, event.reason);
    this.isConnecting = false;
    this.clearTimers();

    // Notify handlers
    this.onCloseHandlers.forEach((handler) => {
      try {
        handler(event);
      } catch (error) {
        console.error("Error in WebSocket close handler:", error);
      }
    });

    // Enhanced reconnection logic for specific close codes
    const shouldReconnect =
      !this.isDestroyed && this.shouldAttemptReconnection(event.code);

    console.log(
      `[WebSocket] Connection closed with code ${event.code} (${this.getCloseCodeDescription(event.code)}). Should reconnect: ${shouldReconnect}`
    );

    if (shouldReconnect) {
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket error event
   */
  private handleError(error: Event): void {
    console.error("WebSocket error:", error);
    this.isConnecting = false;

    // Notify handlers
    this.onErrorHandlers.forEach((handler) => {
      try {
        handler(error);
      } catch (err) {
        console.error("Error in WebSocket error handler:", err);
      }
    });
  }

  /**
   * Handle WebSocket message event
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);

      // Notify handlers
      this.onMessageHandlers.forEach((handler) => {
        try {
          handler(message);
        } catch (error) {
          console.error("Error in WebSocket message handler:", error);
        }
      });
    } catch (error) {
      console.error("Failed to parse WebSocket message:", error);
    }
  }

  /**
   * Schedule reconnection attempt with exponential backoff
   * Initial: 1s, Max: 30s, Multiplier: 2x as requested
   */
  private scheduleReconnect(): void {
    // When maxReconnectAttempts is -1, allow unlimited attempts
    const hasMax = this.config.maxReconnectAttempts >= 0;
    if (hasMax && this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error(
        `[WebSocket] Max reconnection attempts (${this.config.maxReconnectAttempts}) reached`
      );
      return;
    }

    this.reconnectAttempts++;

    // Exponential backoff: initial 1s, max 30s, multiplier 2x
    const baseDelay = this.config.reconnectInterval; // default 5000ms
    const maxDelay = 30000; // 30 seconds max
    const multiplier = 2;

    let delay = Math.min(
      baseDelay * Math.pow(multiplier, this.reconnectAttempts - 1),
      maxDelay
    );
    // Add 10% jitter to prevent thundering herd
    const jitter = Math.random() * 0.1 * delay;
    delay = Math.floor(delay + jitter);

    console.log(
      `[WebSocket] Scheduling reconnection attempt ${this.reconnectAttempts}/${this.config.maxReconnectAttempts} in ${delay}ms`
    );

  this.reconnectTimer = setTimeout(() => {
      console.log(
        `[WebSocket] Attempting reconnection ${this.reconnectAttempts}...`
      );
      this.connect().catch((error) => {
        console.error(
          `[WebSocket] Reconnection attempt ${this.reconnectAttempts} failed:`,
          error
        );
      });
    }, delay);
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: "ping", data: null });
      }
    }, this.config.heartbeatInterval);
  }

  /**
   * Send all queued messages
   */
  private flushMessageQueue(): void {
    while (
      this.messageQueue.length > 0 &&
      this.ws?.readyState === WebSocket.OPEN
    ) {
      const message = this.messageQueue.shift();
      if (message) {
        try {
          this.ws.send(JSON.stringify(message));
        } catch (error) {
          console.error("Failed to send queued message:", error);
          // Put message back at front of queue
          this.messageQueue.unshift(message);
          break;
        }
      }
    }
  }

  /**
   * Determine if reconnection should be attempted based on close code
   */
  private shouldAttemptReconnection(closeCode: number): boolean {
    // Reconnect on these specific codes as requested: 1000, 1001, 1006
    const reconnectCodes = [
      1000, // Normal closure
      1001, // Going away
      1006, // Abnormal closure
      1011, // Server error
      1012, // Service restart
      1013, // Try again later
      1014, // Bad gateway
    ];

    return reconnectCodes.includes(closeCode);
  }

  /**
   * Get human-readable description of close code
   */
  private getCloseCodeDescription(closeCode: number): string {
    const descriptions: { [key: number]: string } = {
      1000: "Normal closure",
      1001: "Going away",
      1002: "Protocol error",
      1003: "Unsupported data",
      1004: "Reserved",
      1005: "No status received",
      1006: "Abnormal closure",
      1007: "Invalid frame payload data",
      1008: "Policy violation",
      1009: "Message too big",
      1010: "Mandatory extension",
      1011: "Internal server error",
      1012: "Service restart",
      1013: "Try again later",
      1014: "Bad gateway",
      1015: "TLS handshake",
    };

    return descriptions[closeCode] || `Unknown (${closeCode})`;
  }

  /**
   * Clear all timers
   */
  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * Clear all event handlers
   */
  private clearEventHandlers(): void {
    this.onOpenHandlers = [];
    this.onCloseHandlers = [];
    this.onErrorHandlers = [];
    this.onMessageHandlers = [];
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * Get current connection state
   */
  getConnectionState(): string {
    if (!this.ws) return "DISCONNECTED";

    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return "CONNECTING";
      case WebSocket.OPEN:
        return "CONNECTED";
      case WebSocket.CLOSING:
        return "CLOSING";
      case WebSocket.CLOSED:
        return "CLOSED";
      default:
        return "UNKNOWN";
    }
  }
}

// Export singleton instance with tuned defaults
export const websocketManager = new WebSocketManager({
  reconnectInterval: 3000,          // base delay 3s (with exponential backoff + jitter)
  maxReconnectAttempts: -1,         // unlimited retries
  heartbeatInterval: 30000,         // keep existing 30s heartbeat
});
export default WebSocketManager;
