@echo off
echo ================================================================
echo   ComfyUI Frontend Backend-Only Startup
echo   Generated: 2025-08-09 05:47:00
echo   System Connections Manager - Backend Startup Script
echo ================================================================
echo.

REM Set up environment variables
set PROJECT_ROOT=G:\comfyui_Front
set BACKEND_VENV=%PROJECT_ROOT%\backend\venv
set BACKEND_DIR=%PROJECT_ROOT%\backend

echo [1/3] Checking backend virtual environment...
if not exist "%BACKEND_VENV%\Scripts\activate.bat" (
    echo ERROR: Backend virtual environment not found at %BACKEND_VENV%
    echo Please run the dependency setup first.
    pause
    exit /b 1
)

echo [2/3] Checking for port conflicts on 8000...
netstat -ano | findstr :8000 >nul
if %errorlevel% == 0 (
    echo WARNING: Port 8000 is already in use. Attempting cleanup...
    for /f "tokens=5" %%i in ('netstat -ano ^| findstr :8000') do (
        echo Terminating process %%i
        taskkill /PID %%i /F >nul 2>&1
    )
    timeout /t 2 >nul
)

echo [3/3] Starting Backend API Server...
echo.
echo ===============================================
echo  Backend API will be available at:
echo  http://localhost:8000
echo  Health check: http://localhost:8000/health
echo ===============================================
echo.

cd /d "%BACKEND_DIR%"
call "%BACKEND_VENV%\Scripts\activate.bat"

REM Verify Python environment
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')" 2>nul
if %errorlevel% neq 0 (
    echo WARNING: PyTorch verification failed. Image generation may not work.
)

REM Start the backend server
echo Starting FastAPI backend server...
python main.py

echo.
echo Backend server stopped.
pause
