@echo off
echo === PowerShell Crash Emergency Fix ===
echo.

echo 1. Killing all PowerShell processes...
taskkill /F /IM pwsh.exe >nul 2>&1
taskkill /F /IM powershell.exe >nul 2>&1
echo    Done.

echo.
echo 2. Clearing PowerShell module cache...
if exist "%LOCALAPPDATA%\Microsoft\Windows\PowerShell\ModuleAnalysisCache" (
    rd /s /q "%LOCALAPPDATA%\Microsoft\Windows\PowerShell\ModuleAnalysisCache"
    echo    Module cache cleared.
) else (
    echo    No module cache found.
)

echo.
echo 3. Resetting VS Code PowerShell extension...
if exist "%USERPROFILE%\.vscode\extensions" (
    echo    VS Code extensions directory found.
    echo    Restart VS Code to reload PowerShell extension.
) else (
    echo    VS Code extensions directory not found.
)

echo.
echo 4. Testing minimal PowerShell session...
pwsh -NoProfile -Command "Write-Host 'PowerShell test successful' -ForegroundColor Green; exit 0"
if %ERRORLEVEL% EQU 0 (
    echo    ✅ PowerShell is working correctly.
) else (
    echo    ❌ PowerShell test failed. Exit code: %ERRORLEVEL%
)

echo.
echo === Emergency Fix Complete ===
echo.
echo Next Steps:
echo 1. Restart VS Code completely
echo 2. Open a new PowerShell terminal
echo 3. If issues persist, run: scripts\powershell_diagnostic.ps1
echo.
pause
