@echo off
echo ================================================================
echo   ComfyUI Frontend Frontend-Only Startup
echo   Generated: 2025-08-09 05:47:00
echo   System Connections Manager - Frontend Startup Script
echo ================================================================
echo.

REM Set up environment variables
set PROJECT_ROOT=G:\comfyui_Front
set FRONTEND_DIR=%PROJECT_ROOT%\frontend

echo [1/3] Checking frontend directory...
if not exist "%FRONTEND_DIR%\package.json" (
    echo ERROR: Frontend directory not found at %FRONTEND_DIR%
    echo Please ensure the frontend is properly set up.
    pause
    exit /b 1
)

echo [2/3] Checking for port conflicts on 3003...
netstat -ano | findstr :3003 >nul
if %errorlevel% == 0 (
    echo WARNING: Port 3003 is already in use. Attempting cleanup...
    for /f "tokens=5" %%i in ('netstat -ano ^| findstr :3003') do (
        echo Terminating process %%i
        taskkill /PID %%i /F >nul 2>&1
    )
    timeout /t 2 >nul
)

echo [3/3] Starting Frontend Development Server...
echo.
echo ===============================================
echo  Frontend will be available at:
echo  http://localhost:3003
echo ===============================================
echo.

cd /d "%FRONTEND_DIR%"

REM Check if node_modules exists, install if needed
if not exist "node_modules" (
    echo Installing frontend dependencies...
    call npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install frontend dependencies
        pause
        exit /b 1
    )
)

REM Verify Node.js and npm
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available
    pause
    exit /b 1
)

REM Start the frontend development server
echo Starting Next.js development server...
call npm run dev

echo.
echo Frontend server stopped.
pause
