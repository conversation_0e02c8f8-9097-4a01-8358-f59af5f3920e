# PowerShell Safety Profile for VS Code
# This script prevents common causes of PowerShell crashes in VS Code
# Place this in your PowerShell profile if needed

# Prevent PSReadLine from causing issues in VS Code
if ($env:TERM_PROGRAM -eq "vscode") {
    # Disable PSReadLine in VS Code integrated terminal to prevent conflicts
    if (Get-Module PSReadLine) {
        Remove-Module PSReadLine -Force
    }
    
    # Set a minimal prompt to prevent recursion
    function prompt {
        "PS $($PWD.Path)> "
    }
    
    # Disable automatic module importing that can cause delays
    $PSModuleAutoLoadingPreference = 'None'
    
    # Set console output encoding to prevent Unicode issues
    $OutputEncoding = [System.Text.Encoding]::UTF8
    [Console]::OutputEncoding = [System.Text.Encoding]::UTF8
    
    # Prevent automatic module analysis
    $env:PSDisableModuleAnalysisCacheCleanup = $true
    
    Write-Host "VS Code PowerShell Safety Profile Loaded" -ForegroundColor Green
}

# Function to safely test commands before execution
function Test-CommandSafety {
    param([string]$Command)
    
    # Check for potentially recursive patterns
    $dangerousPatterns = @(
        "prompt.*prompt",
        "Get-Prompt.*Get-Prompt", 
        "function.*prompt.*prompt",
        "\$PROFILE.*\$PROFILE"
    )
    
    foreach ($pattern in $dangerousPatterns) {
        if ($Command -match $pattern) {
            Write-Warning "Potentially recursive command detected: $pattern"
            return $false
        }
    }
    return $true
}

# Safe execution wrapper
function Invoke-SafeCommand {
    param([string]$Command)
    
    if (Test-CommandSafety $Command) {
        try {
            Invoke-Expression $Command
        } catch {
            Write-Error "Command failed safely: $($_.Exception.Message)"
        }
    } else {
        Write-Warning "Command blocked for safety: $Command"
    }
}

# Emergency reset function
function Reset-PowerShellState {
    Write-Host "Resetting PowerShell state for safety..." -ForegroundColor Yellow
    
    # Remove potentially problematic modules
    @("PSReadLine", "posh-git", "oh-my-posh") | ForEach-Object {
        if (Get-Module $_) {
            Remove-Module $_ -Force -ErrorAction SilentlyContinue
        }
    }
    
    # Reset prompt to minimal
    function global:prompt { "PS $($PWD.Path)> " }
    
    # Clear potentially problematic variables
    Remove-Variable -Name "PSReadLineOption" -ErrorAction SilentlyContinue -Scope Global
    
    Write-Host "PowerShell state reset complete" -ForegroundColor Green
}

# Export functions for use
Export-ModuleMember -Function Test-CommandSafety, Invoke-SafeCommand, Reset-PowerShellState
