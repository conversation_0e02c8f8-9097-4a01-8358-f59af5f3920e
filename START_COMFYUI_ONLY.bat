@echo off
echo ================================================================
echo   ComfyUI Frontend ComfyUI-Only Startup
echo   Generated: 2025-08-09 05:47:00
echo   System Connections Manager - ComfyUI Startup Script
echo ================================================================
echo.

REM Set up environment variables
set COMFYUI_ROOT=G:\PORT_COMFY_front\ComfyUI_windows_portable

echo [1/3] Checking ComfyUI installation...
if not exist "%COMFYUI_ROOT%\python_embeded\python.exe" (
    echo ERROR: ComfyUI installation not found at %COMFYUI_ROOT%
    echo Please ensure ComfyUI is properly installed.
    pause
    exit /b 1
)

echo [2/3] Checking for port conflicts on 8188...
netstat -ano | findstr :8188 >nul
if %errorlevel% == 0 (
    echo WARNING: Port 8188 is already in use. Attempting cleanup...
    for /f "tokens=5" %%i in ('netstat -ano ^| findstr :8188') do (
        echo Terminating process %%i
        taskkill /PID %%i /F >nul 2>&1
    )
    timeout /t 2 >nul
)

echo [3/3] Starting ComfyUI Service...
echo.
echo ===============================================
echo  ComfyUI will be available at:
echo  http://localhost:8188
echo  DO NOT open this directly - use the custom frontend!
echo ===============================================
echo.

cd /d "%COMFYUI_ROOT%"

REM Suppress xformers error dialogs
if exist "G:\comfyui_Front\suppress_xformers_error.py" (
    python "G:\comfyui_Front\suppress_xformers_error.py"
) else (
    set SEM_FAILCRITICALERRORS=1
    set SEM_NOGPFAULTERRORBOX=1
)

REM Start ComfyUI with optimized settings for RTX 4070 Ti SUPER
echo Starting ComfyUI backend service...
.\python_embeded\python.exe -s ComfyUI\main.py ^
    --windows-standalone-build ^
    --highvram ^
    --use-split-cross-attention ^
    --fp16-vae ^
    --dont-upcast-attention ^
    --fast-mode ^
    --listen 0.0.0.0 ^
    --port 8188 ^
    --disable-auto-launch

echo.
echo ComfyUI service stopped.
pause
