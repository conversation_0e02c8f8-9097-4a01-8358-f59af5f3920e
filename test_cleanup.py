#!/usr/bin/env python3
"""
Test script for the cleanup functionality
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'agent_orchestration', 'scripts'))

from connections_manager import SystemConnectionsManager

def test_cleanup_functionality():
    """Test the cleanup functionality in dry run mode"""
    manager = SystemConnectionsManager()
    
    print("Testing cleanup functionality (dry run mode)...")
    
    # Test with default parameters (dry run)
    result = manager.execute_task("cleanup_obsolete_scripts", {
        "dry_run": True,
        "cleanup_scope": "startup_shutdown"
    })
    
    print(f"Result Success: {result.success}")
    print(f"Result Message: {result.message}")
    
    if result.data:
        print(f"Obsolete files found: {len(result.data.get('obsolete_files_found', []))}")
        print(f"Files preserved: {len(result.data.get('files_preserved', []))}")
        
        # Show details of obsolete files
        for file_info in result.data.get('obsolete_files_found', []):
            print(f"  - {file_info['path']} ({file_info['reason']})")
            
        # Show preserved files
        for preserved_file in result.data.get('files_preserved', []):
            print(f"  + Preserved: {preserved_file}")

if __name__ == "__main__":
    test_cleanup_functionality()
