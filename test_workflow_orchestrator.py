#!/usr/bin/env python3
"""
Test script to verify workflow orchestrator with model verification
"""
import asyncio
import sys
from pathlib import Path

# Add the scripts directory to path
sys.path.append(str(Path(__file__).parent / "agent_orchestration" / "scripts"))

from workflow_orchestrator import WorkflowOrchestratorAgent

async def test_workflow_orchestrator():
    """Test workflow orchestrator initialization with model verification"""
    print("🔍 Testing workflow orchestrator initialization...")
    print("=" * 60)
    
    try:
        # Test context for FLUX generation with proper structure
        test_context = {
            "agent": {
                "name": "workflow_orchestrator_test"
            },
            "task": {
                "name": "flux_generation_test",
                "parameters": {
                    "prompt": "test image",
                    "model": "flux1-dev.safetensors",
                    "clip_model": "flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors",
                    "vae": "ae.safetensors",
                    "model_type": "flux"
                }
            },
            "config": {
                "data_dir": "g:/comfyui_Front/data",
                "databases": [],
                "project_root": "g:/comfyui_Front"
            }
        }
        
        # Create orchestrator instance with context
        orchestrator = WorkflowOrchestratorAgent(test_context)
        
        print(f"✅ Orchestrator initialized successfully")
        print(f"📋 Test context: {test_context['task']['name']}")
        print(f"🎯 Model type: {test_context['task']['parameters']['model_type']}")
        print(f"🎨 CLIP model: {test_context['task']['parameters']['clip_model']}")
        
        # Test model verification
        print("\n🔍 Running model verification...")
        verification_result = await orchestrator._verify_model_files()
        
        files_checked = verification_result.get('files_checked', 0)
        files_renamed = verification_result.get('files_renamed', 0)
        missing_files = verification_result.get('missing_critical_files', [])
        
        print(f"📊 Files checked: {files_checked}")
        print(f"🔄 Files renamed: {files_renamed}")
        print(f"❌ Missing files: {len(missing_files)}")
        
        if missing_files:
            print("\n⚠️ Missing critical files:")
            for file in missing_files:
                print(f"  - {file}")
        
        # Check if FLUX CLIP model is found
        flux_clip_found = False
        print(f"\n🔍 Checking inventory structure...")
        
        if 'model_inventory' in verification_result:
            print(f"📦 Model inventory sections: {list(verification_result['model_inventory'].keys())}")
            
            # Try both CLIP and clip
            clip_files = verification_result['model_inventory'].get('CLIP', [])
            if not clip_files:
                clip_files = verification_result['model_inventory'].get('clip', [])
            
            print(f"📎 CLIP files found: {len(clip_files)}")
            
            for i, file_info in enumerate(clip_files):
                print(f"  {i+1}. {file_info}")
                if 'flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors' in str(file_info):
                    flux_clip_found = True
                    print(f"✅ FLUX CLIP model found: {file_info}")
                    
            # Also check all sections to see where FLUX clip might be
            for section, files in verification_result['model_inventory'].items():
                for file_info in files:
                    if 'flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors' in str(file_info):
                        print(f"🎯 Found FLUX CLIP in section '{section}': {file_info}")
                        flux_clip_found = True
        else:
            print("❌ No model_inventory section found in verification result!")
            print(f"📋 Verification result keys: {list(verification_result.keys())}")
        
        if not flux_clip_found:
            print("\n❌ FLUX CLIP model not found in inventory!")
        
        print("\n" + "=" * 60)
        print("Workflow orchestrator test complete!")
        
        return {
            "success": True,
            "files_checked": files_checked,
            "files_renamed": files_renamed,
            "missing_files": len(missing_files),
            "flux_clip_found": flux_clip_found
        }
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    result = asyncio.run(test_workflow_orchestrator())
    print(f"\nTest result: {result}")
