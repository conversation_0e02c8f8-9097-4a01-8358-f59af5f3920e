#!/usr/bin/env python3
"""
Requirements Manager Agent
Specialized agent for managing requirements.txt files and Python dependencies

This agent handles:
1. Requirements.txt file management and optimization
2. Dependency version conflict resolution
3. Python package ecosystem analysis
4. Virtual environment management recommendations
"""

import asyncio
import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import re

# Import base agent functionality
sys.path.insert(0, str(Path(__file__).parent))
from base_agent import BaseAgent

class RequirementsManagerAgent(BaseAgent):
    """
    Requirements Manager Agent implementation.
    
    Handles Python package requirements management, dependency analysis,
    and environment optimization for the ComfyUI Frontend project.
    """
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.project_root = Path(context['config']['project_root'])
        
        # Environment paths
        self.backend_path = self.project_root / "backend"
        self.requirements_path = self.backend_path / "requirements.txt"
        self.backend_venv_path = self.project_root / "backend" / "venv"
    
    async def execute_task(self) -> Dict[str, Any]:
        """Execute the specific requirements manager task."""
        task_name = self.task_name
        
        if task_name == "analyze_requirements":
            return await self.analyze_requirements()
        elif task_name == "optimize_dependencies":
            return await self.optimize_dependencies()
        elif task_name == "resolve_conflicts":
            return await self.resolve_conflicts()
        elif task_name == "audit_security":
            return await self.audit_security()
        else:
            return await self.default_requirements_analysis()
    
    async def analyze_requirements(self) -> Dict[str, Any]:
        """Analyze requirements.txt files for optimization opportunities."""
        self.logger.info("🔍 Analyzing Python requirements and dependencies...")
        
        analysis_results = {
            "requirements_files": [],
            "dependency_analysis": {},
            "optimization_opportunities": [],
            "security_issues": [],
            "version_conflicts": []
        }
        
        # Find all requirements files
        req_files = list(self.project_root.glob("**/requirements*.txt"))
        req_files.extend(list(self.project_root.glob("**/Pipfile*")))
        req_files.extend(list(self.project_root.glob("**/pyproject.toml")))
        
        for req_file in req_files:
            file_analysis = await self._analyze_requirements_file(req_file)
            analysis_results["requirements_files"].append({
                "file": str(req_file),
                "analysis": file_analysis
            })
        
        # Analyze dependencies across files
        if analysis_results["requirements_files"]:
            dependency_analysis = await self._cross_file_dependency_analysis(analysis_results["requirements_files"])
            analysis_results["dependency_analysis"] = dependency_analysis
        
        return {
            "success": True,
            "analysis": analysis_results,
            "recommendations": await self._generate_requirements_recommendations(analysis_results),
            "timestamp": datetime.now().isoformat()
        }
    
    async def _analyze_requirements_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyze a single requirements file."""
        analysis = {
            "packages_count": 0,
            "pinned_versions": 0,
            "unpinned_versions": 0,
            "packages": [],
            "issues": []
        }
        
        if not file_path.exists():
            analysis["issues"].append(f"File does not exist: {file_path}")
            return analysis
        
        try:
            content = file_path.read_text(encoding='utf-8')
            lines = [line.strip() for line in content.split('\n') if line.strip() and not line.startswith('#')]
            
            for line in lines:
                if '==' in line:
                    analysis["pinned_versions"] += 1
                    package_name = line.split('==')[0].strip()
                    version = line.split('==')[1].strip()
                    analysis["packages"].append({
                        "name": package_name,
                        "version": version,
                        "pinned": True
                    })
                elif any(op in line for op in ['>=', '<=', '>', '<', '~=']):
                    analysis["unpinned_versions"] += 1
                    # Parse package name from complex version spec
                    package_name = re.split(r'[><=~!]', line)[0].strip()
                    analysis["packages"].append({
                        "name": package_name,
                        "version_spec": line,
                        "pinned": False
                    })
                else:
                    analysis["unpinned_versions"] += 1
                    analysis["packages"].append({
                        "name": line.strip(),
                        "version": "latest",
                        "pinned": False
                    })
            
            analysis["packages_count"] = len(analysis["packages"])
            
        except Exception as e:
            analysis["issues"].append(f"Error reading file: {str(e)}")
        
        return analysis
    
    async def _cross_file_dependency_analysis(self, files_analysis: List[Dict]) -> Dict[str, Any]:
        """Analyze dependencies across multiple requirements files."""
        all_packages = {}
        conflicts = []
        
        for file_data in files_analysis:
            file_path = file_data["file"]
            packages = file_data["analysis"].get("packages", [])
            
            for package in packages:
                package_name = package["name"]
                
                if package_name in all_packages:
                    existing = all_packages[package_name]
                    if existing["version"] != package.get("version") and package.get("pinned", False):
                        conflicts.append({
                            "package": package_name,
                            "file1": existing["file"],
                            "version1": existing["version"],
                            "file2": file_path,
                            "version2": package.get("version")
                        })
                else:
                    all_packages[package_name] = {
                        "file": file_path,
                        "version": package.get("version"),
                        "pinned": package.get("pinned", False)
                    }
        
        return {
            "total_unique_packages": len(all_packages),
            "version_conflicts": conflicts,
            "packages_summary": all_packages
        }
    
    async def _generate_requirements_recommendations(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate optimization recommendations based on analysis."""
        recommendations = []
        
        # Check for unpinned versions
        for file_data in analysis.get("requirements_files", []):
            file_analysis = file_data["analysis"]
            if file_analysis.get("unpinned_versions", 0) > 0:
                recommendations.append({
                    "type": "version_pinning",
                    "priority": "medium",
                    "description": f"Consider pinning versions in {file_data['file']} for reproducible builds",
                    "action": "Pin specific versions for critical dependencies"
                })
        
        # Check for conflicts
        conflicts = analysis.get("dependency_analysis", {}).get("version_conflicts", [])
        if conflicts:
            for conflict in conflicts:
                recommendations.append({
                    "type": "version_conflict",
                    "priority": "high",
                    "description": f"Version conflict for {conflict['package']} between files",
                    "action": f"Resolve version mismatch: {conflict['version1']} vs {conflict['version2']}"
                })
        
        return recommendations
    
    async def optimize_dependencies(self) -> Dict[str, Any]:
        """Optimize Python dependencies for performance and security."""
        self.logger.info("⚡ Optimizing Python dependencies...")
        
        optimizations = {
            "removed_duplicates": [],
            "updated_versions": [],
            "performance_improvements": [],
            "size_reductions": []
        }
        
        # This would contain actual optimization logic
        # For now, return analysis results
        
        return {
            "success": True,
            "optimizations": optimizations,
            "recommendations": [
                {
                    "type": "performance",
                    "description": "Consider using pip-tools for dependency management",
                    "priority": "medium"
                }
            ],
            "timestamp": datetime.now().isoformat()
        }
    
    async def resolve_conflicts(self) -> Dict[str, Any]:
        """Resolve dependency conflicts."""
        self.logger.info("🔧 Resolving dependency conflicts...")
        
        # Analyze current conflicts
        analysis = await self.analyze_requirements()
        conflicts = analysis.get("analysis", {}).get("dependency_analysis", {}).get("version_conflicts", [])
        
        resolutions = []
        for conflict in conflicts:
            resolution = {
                "package": conflict["package"],
                "recommended_version": "latest compatible",
                "strategy": "update to latest compatible version",
                "impact": "low"
            }
            resolutions.append(resolution)
        
        return {
            "success": True,
            "conflicts_found": len(conflicts),
            "resolutions": resolutions,
            "timestamp": datetime.now().isoformat()
        }
    
    async def audit_security(self) -> Dict[str, Any]:
        """Audit dependencies for security vulnerabilities."""
        self.logger.info("🔒 Auditing dependencies for security issues...")
        
        security_results = {
            "vulnerabilities_found": 0,
            "critical_issues": [],
            "recommendations": []
        }
        
        # This would integrate with safety, bandit, or similar tools
        security_results["recommendations"].append({
            "type": "security",
            "description": "Run 'pip install safety && safety check' for vulnerability scanning",
            "priority": "high"
        })
        
        return {
            "success": True,
            "security_audit": security_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def default_requirements_analysis(self) -> Dict[str, Any]:
        """Default analysis task."""
        return await self.analyze_requirements()

# Entry point for orchestration system
async def execute(context):
    """Execute function required by the orchestration system."""
    agent = RequirementsManagerAgent(context)
    return await agent.execute_task()

if __name__ == "__main__":
    # Test execution
    test_context = {
        "config": {"project_root": str(Path(__file__).parent.parent.parent)},
        "task": {"name": "analyze_requirements"}
    }
    
    import asyncio
    result = asyncio.run(execute(test_context))
    print(json.dumps(result, indent=2))
