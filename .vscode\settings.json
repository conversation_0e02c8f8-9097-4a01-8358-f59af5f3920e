{"files.associations": {"README_DEVELOPMENT_REFERENCE.md": "markdown", "*_Solutions.md": "markdown"}, "editor.quickSuggestions": {"comments": true, "strings": true, "other": true}, "files.exclude": {"**/node_modules": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/__pycache__": true, "**/*.pyc": true}, "search.exclude": {"**/node_modules": true, "**/bower_components": true, "**/.git": true, "**/dist": true, "**/build": true, "**/*.min.js": true, "**/coverage": true, "**/__pycache__": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/__pycache__/**": true}, "markdown.suggest.paths.enabled": true, "markdown.validate.enabled": true, "markdown.extension.toc.levels": "1..6", "workbench.startupEditor": "readme", "workbench.tips.enabled": true, "editor.suggest.showSnippets": true, "editor.suggest.showWords": true, "editor.wordWrap": "on", "editor.rulers": [80, 120], "git.autofetch": true, "git.enableSmartCommit": true, "git.confirmSync": false, "terminal.integrated.defaultProfile.windows": "Command Prompt", "terminal.integrated.shellIntegration.enabled": true, "terminal.integrated.shellIntegration.decorationsEnabled": "both", "terminal.integrated.shellIntegration.showWelcome": false, "powershell.enableProfileLoading": false, "powershell.integratedConsole.showOnStartup": false, "powershell.powerShellDefaultVersion": "PowerShell (x64)", "powershell.enableReferencesCodeLens": false, "powershell.integratedConsole.suppressStartupBanner": true, "powershell.integratedConsole.startInBackground": true, "powershell.debugging.createTemporaryIntegratedConsole": false, "terminal.integrated.commandsToSkipShell": ["language-julia.interrupt"], "terminal.integrated.enableMultiLinePasteWarning": false, "terminal.integrated.copyOnSelection": true, "terminal.integrated.cursorBlinking": true, "terminal.integrated.fontSize": 14, "terminal.integrated.shellIntegration.history": 100, "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "python.defaultInterpreterPath": "./Comfyvenv/Scripts/python.exe", "eslint.workingDirectories": ["frontend"], "prettier.configPath": "./frontend/.prettierrc", "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "explorer.fileNesting.enabled": true, "explorer.fileNesting.patterns": {"package.json": "package-lock.json,yarn.lock,pnpm-lock.yaml", "README.md": "README_*.md", "*.md": "*.solutions.md", "tsconfig.json": "tsconfig.*.json", ".env": ".env.*", "*.component.tsx": "*.component.css,*.component.scss,*.component.test.tsx"}, "todo-tree.general.tags": ["BUG", "HACK", "FIXME", "TODO", "XXX", "[ ]", "[x]", "REFERENCE:", "DOC:", "SEE:"], "todo-tree.highlights.customHighlight": {"REFERENCE:": {"icon": "book", "iconColour": "#4CAF50", "foreground": "#4CAF50", "background": "#E8F5E8"}, "DOC:": {"icon": "file-text", "iconColour": "#2196F3", "foreground": "#2196F3", "background": "#E3F2FD"}, "SEE:": {"icon": "arrow-right", "iconColour": "#FF9800", "foreground": "#FF9800", "background": "#FFF3E0"}}, "workbench.colorCustomizations": {"editorRuler.foreground": "#2C2C2C40"}}