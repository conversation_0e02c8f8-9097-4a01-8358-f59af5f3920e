# ComfyUI Custom Frontend

A professional, hardware-optimized frontend interface for ComfyUI with AI-powered creative features.

## Features

- **Multiple Generation Modes**: Text-to-image, Image-to-image, Inpainting, Outpainting
- **AI Creative Mode**: LLM-powered prompt generation with Ollama integration
- **Reference Image Analysis**: Use existing images as style/composition guides
- **Image Enhancement**: 4x upscaling and quality enhancement
- **Hardware Optimized**: Designed for RTX 4070 Ti SUPER + 64GB RAM

## Tech Stack

- **Frontend**: Next.js 14 + TypeScript + Tailwind CSS
- **Backend**: FastAPI + WebSocket support
- **AI Integration**: Ollama LLM (gpt-oss:latest)
- **Image Processing**: ComfyUI API integration

## 🚨 Important for Developers

**Before editing UI components, read this first:**
- 📖 **[Quick Reference: UI Editing Guide](docs/QUICK_REFERENCE_UI_EDITING.md)** - Essential guide to avoid common mistakes
- 📖 **[Right Panel Architecture](docs/RIGHT_PANEL_ARCHITECTURE.md)** - Detailed documentation of the contextual menu system

**Key Point**: The main app uses `Layout.tsx`, NOT `DashboardIsolated.tsx`. Multiple attempts to edit the wrong file have failed in the past.

## 📚 **DEVELOPMENT REFERENCE LIBRARY**

**🚨 BEFORE YOU START CODING - CHECK HERE FIRST!**

➡️ **[📖 Development Reference Guide](README_DEVELOPMENT_REFERENCE.md)** - **START HERE!**

**Quick Links:**
- 🔧 **Frontend-Backend Issues?** → [Frontend_Backend_Solutions.md](Frontend_Backend_Solutions.md)
- 📦 **Dependency Conflicts?** → [Dependency_Resolution_Solutions.md](Dependency_Resolution_Solutions.md)  
- ⚛️ **React State Problems?** → [UI_State_Management_Solutions.md](UI_State_Management_Solutions.md)
- 🤖 **ComfyUI Workflow Issues?** → [ComfyUI_Workflow_Orchestration_Solutions.md](ComfyUI_Workflow_Orchestration_Solutions.md)

**💡 These documents contain verified solutions that will save you hours of debugging!**

## Project Structure

```
comfyui_custom/
├── frontend/           # Next.js frontend application
├── backend/            # FastAPI backend server
├── docs/              # Documentation
├── configs/           # Configuration files
└── README.md          # This file
```
## IMPORTANT INFORMATION: This project will utilize the backend from the portable comfyui at G:/PORT_COMFY_front/ComfyUI_windows_portable/. However, All models are linked from L:/ComfyUI/models

## Hardware Requirements

- GPU: NVIDIA RTX 4070 Ti SUPER (16GB VRAM)
- RAM: 64GB DDR5
- Storage: Multi-drive setup (C:/, L:/, G:/)
- CUDA: 12.8+

## Getting Started

### 🚀 Universal Startup (Recommended)

**Single command to start everything:**
```bat
START.bat
```
This will automatically:
- Start ComfyUI backend (port 8188) with auto-launch disabled
- Start FastAPI middleware (port 8000) 
- Start frontend development server (port 3003)
- Open browser to http://localhost:3003

**To stop all services:**
```bat
STOP.bat
```

### 📋 Alternative Commands
```bash
# Using npm (same as START.bat)
npm start

# Stop services
npm run stop

# Check connection status
npm run check

# View documentation
npm run docs
```

### 🔧 Manual Setup (If Needed)
1. **Backend (Python 3.11 + Virtual Environment):**
   ```powershell
   cd backend
   python -m venv venv
   .\venv\Scripts\Activate.ps1
   pip install -r requirements.txt
   ```

2. **Frontend:**
   ```powershell
   cd frontend
   npm install
   ```

3. **Environment Setup:**
   ```powershell
   .\setup_environment.ps1
   ```

### ⚠️ Important Notes
- **Use ONLY http://localhost:3003** (custom frontend)
- **DO NOT open http://localhost:8188** (ComfyUI backend API only)
- Auto-launch is disabled to prevent browser conflicts
- If you see red connection lights, run `npm run check` for diagnosis
