/**
 * useGeneration Hook
 * React hook for managing text-to-image generation with real-time progress updates
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  generationApiService, 
  GenerationPayload, 
  GenerationProgress, 
  GenerationResult, 
  GenerationState, 
  GenerationStatus 
} from '../services/generationApiService';

export interface UseGenerationOptions {
  autoConnect?: boolean;
  onProgress?: (progress: GenerationProgress) => void;
  onComplete?: (result: GenerationResult) => void;
  onError?: (error: { generation_id: string; error: string; error_code?: string; details?: any }) => void;
}

export interface UseGenerationReturn {
  // State
  isGenerating: boolean;
  currentGeneration: string | null;
  progress: number;
  state: GenerationState | null;
  substage: string | null;
  message: string | null;
  error: string | null;
  result: GenerationResult | null;
  
  // Actions
  startGeneration: (payload: GenerationPayload) => Promise<string | null>;
  cancelGeneration: () => Promise<void>;
  clearResult: () => void;
  
  // WebSocket
  isWebSocketConnected: boolean;
  connectWebSocket: () => Promise<void>;
  disconnectWebSocket: () => void;
  
  // History
  history: GenerationStatus[];
  refreshHistory: () => Promise<void>;
}

export const useGeneration = (options: UseGenerationOptions = {}): UseGenerationReturn => {
  const { autoConnect = true, onProgress, onComplete, onError } = options;
  
  // State
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentGeneration, setCurrentGeneration] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);
  const [state, setState] = useState<GenerationState | null>(null);
  const [substage, setSubstage] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<GenerationResult | null>(null);
  const [isWebSocketConnected, setIsWebSocketConnected] = useState(false);
  const [history, setHistory] = useState<GenerationStatus[]>([]);
  
  // Refs to avoid stale closures
  const onProgressRef = useRef(onProgress);
  const onCompleteRef = useRef(onComplete);
  const onErrorRef = useRef(onError);
  
  // Update refs when callbacks change
  useEffect(() => {
    onProgressRef.current = onProgress;
  }, [onProgress]);
  
  useEffect(() => {
    onCompleteRef.current = onComplete;
  }, [onComplete]);
  
  useEffect(() => {
    onErrorRef.current = onError;
  }, [onError]);
  
  // Progress callback
  const handleProgress = useCallback((progressData: GenerationProgress) => {
    setProgress(progressData.progress);
    setState(progressData.state);
    setSubstage(progressData.substage || null);
    setMessage(progressData.message || null);
    
    if (progressData.error) {
      setError(progressData.error);
    }
    
    // Call external callback
    onProgressRef.current?.(progressData);
  }, []);
  
  // Complete callback
  const handleComplete = useCallback((resultData: GenerationResult) => {
    setIsGenerating(false);
    setResult(resultData);
    setState(GenerationState.COMPLETED);
    setProgress(100);
    setMessage('Generation completed successfully');
    
    // Call external callback
    onCompleteRef.current?.(resultData);
    
    // Refresh history
    refreshHistory();
  }, []);
  
  // Error callback
  const handleError = useCallback((errorData: { generation_id: string; error: string; error_code?: string; details?: any }) => {
    setIsGenerating(false);
    setState(GenerationState.FAILED);
    setError(errorData.error);
    setMessage(`Generation failed: ${errorData.error}`);
    
    // Call external callback
    onErrorRef.current?.(errorData);
    
    // Refresh history
    refreshHistory();
  }, []);
  
  // WebSocket connection management
  useEffect(() => {
    const checkWebSocketConnection = () => {
      setIsWebSocketConnected(generationApiService.isWebSocketConnected());
    };
    
    checkWebSocketConnection();
  const interval = setInterval(checkWebSocketConnection, 3000);
    
    return () => clearInterval(interval);
  }, []);
  
  // Auto-connect WebSocket
  useEffect(() => {
    if (autoConnect && !isWebSocketConnected) {
      generationApiService.connectWebSocket().catch(console.error);
    }
  }, [autoConnect, isWebSocketConnected]);
  
  // Start generation
  const startGeneration = useCallback(async (payload: GenerationPayload): Promise<string | null> => {
    try {
      // Clear previous state
      setError(null);
      setResult(null);
      setProgress(0);
      setState(null);
      setSubstage(null);
      setMessage(null);
      
      // Ensure WebSocket is connected
      if (!generationApiService.isWebSocketConnected()) {
        await generationApiService.connectWebSocket();
      }
      
      const response = await generationApiService.startGeneration(
        payload,
        handleProgress,
        handleComplete,
        handleError
      );
      
      if (response.success && response.generation_id) {
        setCurrentGeneration(response.generation_id);
        setIsGenerating(true);
        setState(GenerationState.QUEUED);
        setMessage(response.message);
        return response.generation_id;
      } else {
        throw new Error(response.message || 'Failed to start generation');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      setMessage(`Failed to start generation: ${errorMessage}`);
      setIsGenerating(false);
      return null;
    }
  }, [handleProgress, handleComplete, handleError]);
  
  // Cancel generation
  const cancelGeneration = useCallback(async (): Promise<void> => {
    if (!currentGeneration) return;
    
    try {
      await generationApiService.cancelGeneration(currentGeneration);
      setIsGenerating(false);
      setState(GenerationState.CANCELLED);
      setMessage('Generation cancelled');
      
      // Refresh history
      await refreshHistory();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to cancel generation';
      setError(errorMessage);
      console.error('Failed to cancel generation:', err);
    }
  }, [currentGeneration]);
  
  // Clear result
  const clearResult = useCallback(() => {
    setResult(null);
    setError(null);
    setProgress(0);
    setState(null);
    setSubstage(null);
    setMessage(null);
    setCurrentGeneration(null);
    setIsGenerating(false);
  }, []);
  
  // Connect WebSocket
  const connectWebSocket = useCallback(async (): Promise<void> => {
    try {
      await generationApiService.connectWebSocket();
    } catch (err) {
      console.error('Failed to connect WebSocket:', err);
      throw err;
    }
  }, []);
  
  // Disconnect WebSocket
  const disconnectWebSocket = useCallback((): void => {
    generationApiService.disconnectWebSocket();
  }, []);
  
  // Refresh history
  const refreshHistory = useCallback(async (): Promise<void> => {
    try {
      const historyData = await generationApiService.getGenerationHistory(20);
      setHistory(historyData);
    } catch (err) {
      console.error('Failed to refresh history:', err);
    }
  }, []);
  
  // Load history on mount
  useEffect(() => {
    refreshHistory();
  }, [refreshHistory]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (currentGeneration) {
        generationApiService.unregisterCallbacks(currentGeneration);
      }
    };
  }, [currentGeneration]);
  
  return {
    // State
    isGenerating,
    currentGeneration,
    progress,
    state,
    substage,
    message,
    error,
    result,
    
    // Actions
    startGeneration,
    cancelGeneration,
    clearResult,
    
    // WebSocket
    isWebSocketConnected,
    connectWebSocket,
    disconnectWebSocket,
    
    // History
    history,
    refreshHistory
  };
};

export default useGeneration;
