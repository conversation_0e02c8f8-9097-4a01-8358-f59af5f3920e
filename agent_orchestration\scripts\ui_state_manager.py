#!/usr/bin/env python3
"""
UI State Manager Agent
Advanced UI state architecture specialist for React/Next.js frontend optimization

This agent handles:
1. State management pattern analysis and optimization
2. Component performance monitoring
3. State synchronization between frontend and backend
4. UI rendering performance optimization
5. State migration planning and recommendations
"""

import asyncio
import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import re

# Import base agent functionality
sys.path.insert(0, str(Path(__file__).parent))
from base_agent import BaseAgent

class UIStateManagerAgent(BaseAgent):
    """
    UI State Manager Agent implementation.
    
    Analyzes and optimizes UI state management patterns in React/Next.js applications.
    Provides recommendations for performance improvements and state architecture.
    """
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.project_root = Path(context['config']['project_root'])
        
        # Frontend paths
        self.frontend_path = self.project_root / "frontend"
        self.components_path = self.frontend_path / "src" / "components"
        self.stores_path = self.frontend_path / "src" / "stores" 
        self.hooks_path = self.frontend_path / "src" / "hooks"
        self.package_json_path = self.frontend_path / "package.json"
    
    async def execute_task(self) -> Dict[str, Any]:
        """Execute the specific UI state manager task."""
        task_name = self.task_name
        
        if task_name == "analyze_state_architecture":
            return await self.analyze_state_architecture()
        elif task_name == "performance_optimization":
            return await self.performance_optimization()
        elif task_name == "migration_planning":
            return await self.migration_planning()
        elif task_name == "state_synchronization_audit":
            return await self.state_synchronization_audit()
        else:
            return await self.default_state_analysis()
    
    async def analyze_state_architecture(self) -> Dict[str, Any]:
        """Analyze current state management architecture."""
        self.logger.info("🔍 Analyzing UI state architecture...")
        
        analysis_results = {
            "state_patterns": [],
            "performance_issues": [],
            "architecture_recommendations": [],
            "component_analysis": {},
            "state_libraries": []
        }
        
        # Analyze package.json for state management libraries
        if self.package_json_path.exists():
            package_data = await self._analyze_package_json()
            analysis_results["state_libraries"] = package_data.get("state_libraries", [])
        
        # Analyze components for state patterns
        if self.components_path.exists():
            component_analysis = await self._analyze_components()
            analysis_results["component_analysis"] = component_analysis
            
        # Analyze custom hooks
        if self.hooks_path.exists():
            hooks_analysis = await self._analyze_hooks()
            analysis_results["hooks_analysis"] = hooks_analysis
        
        # Analyze stores if they exist
        if self.stores_path.exists():
            stores_analysis = await self._analyze_stores()
            analysis_results["stores_analysis"] = stores_analysis
        
        # Generate recommendations
        recommendations = await self._generate_state_recommendations(analysis_results)
        analysis_results["architecture_recommendations"] = recommendations
        
        return {
            "success": True,
            "analysis": analysis_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _analyze_package_json(self) -> Dict[str, Any]:
        """Analyze package.json for state management dependencies."""
        state_libraries = []
        
        try:
            with open(self.package_json_path, 'r') as f:
                package_data = json.load(f)
            
            dependencies = package_data.get('dependencies', {})
            dev_dependencies = package_data.get('devDependencies', {})
            all_deps = {**dependencies, **dev_dependencies}
            
            # Known state management libraries
            state_lib_patterns = {
                'redux': 'Redux',
                '@reduxjs/toolkit': 'Redux Toolkit',
                'zustand': 'Zustand',
                'react-query': 'React Query',
                '@tanstack/react-query': 'TanStack Query',
                'swr': 'SWR',
                'recoil': 'Recoil',
                'jotai': 'Jotai',
                'valtio': 'Valtio',
                'react-context': 'React Context',
                'mobx': 'MobX',
                'xstate': 'XState'
            }
            
            for dep_name, version in all_deps.items():
                for pattern, lib_name in state_lib_patterns.items():
                    if pattern in dep_name:
                        state_libraries.append({
                            "name": lib_name,
                            "package": dep_name,
                            "version": version,
                            "type": "dependency" if dep_name in dependencies else "devDependency"
                        })
            
        except Exception as e:
            self.logger.error(f"Error analyzing package.json: {e}")
        
        return {"state_libraries": state_libraries}
    
    async def _analyze_components(self) -> Dict[str, Any]:
        """Analyze React components for state patterns."""
        component_analysis = {
            "total_components": 0,
            "state_patterns": {
                "useState": 0,
                "useReducer": 0,
                "useContext": 0,
                "custom_hooks": 0,
                "external_state": 0
            },
            "performance_issues": [],
            "recommendations": []
        }
        
        try:
            # Find all React component files
            component_files = list(self.components_path.glob("**/*.tsx"))
            component_files.extend(list(self.components_path.glob("**/*.jsx")))
            
            component_analysis["total_components"] = len(component_files)
            
            for comp_file in component_files:
                try:
                    content = comp_file.read_text(encoding='utf-8')
                    
                    # Analyze state patterns
                    if 'useState' in content:
                        component_analysis["state_patterns"]["useState"] += content.count('useState')
                    
                    if 'useReducer' in content:
                        component_analysis["state_patterns"]["useReducer"] += content.count('useReducer')
                    
                    if 'useContext' in content:
                        component_analysis["state_patterns"]["useContext"] += content.count('useContext')
                    
                    # Check for custom hooks (use[A-Z])
                    custom_hook_pattern = r'use[A-Z]\w+'
                    custom_hooks = re.findall(custom_hook_pattern, content)
                    component_analysis["state_patterns"]["custom_hooks"] += len(custom_hooks)
                    
                    # Check for external state libraries
                    external_patterns = ['useSelector', 'useStore', 'useQuery', 'useSWR']
                    for pattern in external_patterns:
                        if pattern in content:
                            component_analysis["state_patterns"]["external_state"] += content.count(pattern)
                    
                    # Check for potential performance issues
                    await self._check_component_performance_issues(comp_file, content, component_analysis)
                    
                except Exception as e:
                    self.logger.warning(f"Error analyzing component {comp_file}: {e}")
        
        except Exception as e:
            self.logger.error(f"Error analyzing components directory: {e}")
        
        return component_analysis
    
    async def _check_component_performance_issues(self, file_path: Path, content: str, analysis: Dict[str, Any]):
        """Check for potential performance issues in components."""
        issues = []
        
        # Check for missing React.memo on components with props
        if 'export default function' in content or 'export const' in content:
            if 'React.memo' not in content and 'memo(' not in content:
                if 'props' in content:
                    issues.append({
                        "file": str(file_path.relative_to(self.project_root)),
                        "issue": "Component with props not wrapped in React.memo",
                        "severity": "medium",
                        "suggestion": "Consider wrapping with React.memo for performance"
                    })
        
        # Check for inline object/function creation in JSX
        inline_patterns = [
            r'\{\s*\{.*\}\s*\}',  # Inline objects
            r'\{\s*\(.*\)\s*=>\s*.*\}',  # Inline arrow functions
            r'onClick=\{.*=>\s*'  # Inline event handlers
        ]
        
        for pattern in inline_patterns:
            matches = re.findall(pattern, content)
            if matches:
                issues.append({
                    "file": str(file_path.relative_to(self.project_root)),
                    "issue": f"Inline object/function creation detected ({len(matches)} instances)",
                    "severity": "low",
                    "suggestion": "Move objects/functions outside render or use useCallback/useMemo"
                })
        
        analysis["performance_issues"].extend(issues)
    
    async def _analyze_hooks(self) -> Dict[str, Any]:
        """Analyze custom hooks."""
        hooks_analysis = {
            "total_hooks": 0,
            "hook_types": [],
            "state_management_hooks": 0,
            "recommendations": []
        }
        
        try:
            if not self.hooks_path.exists():
                return hooks_analysis
            
            hook_files = list(self.hooks_path.glob("**/*.ts"))
            hook_files.extend(list(self.hooks_path.glob("**/*.tsx")))
            
            hooks_analysis["total_hooks"] = len(hook_files)
            
            for hook_file in hook_files:
                try:
                    content = hook_file.read_text(encoding='utf-8')
                    
                    # Identify hook types
                    if 'useState' in content or 'useReducer' in content:
                        hooks_analysis["state_management_hooks"] += 1
                        hooks_analysis["hook_types"].append({
                            "file": str(hook_file.relative_to(self.project_root)),
                            "type": "state_management"
                        })
                    
                except Exception as e:
                    self.logger.warning(f"Error analyzing hook {hook_file}: {e}")
        
        except Exception as e:
            self.logger.error(f"Error analyzing hooks: {e}")
        
        return hooks_analysis
    
    async def _analyze_stores(self) -> Dict[str, Any]:
        """Analyze state stores (Zustand, Redux, etc.)."""
        stores_analysis = {
            "total_stores": 0,
            "store_types": [],
            "complexity_score": 0,
            "recommendations": []
        }
        
        try:
            if not self.stores_path.exists():
                return stores_analysis
            
            store_files = list(self.stores_path.glob("**/*.ts"))
            store_files.extend(list(self.stores_path.glob("**/*.tsx")))
            
            stores_analysis["total_stores"] = len(store_files)
            
            for store_file in store_files:
                try:
                    content = store_file.read_text(encoding='utf-8')
                    
                    # Identify store types
                    if 'create(' in content or 'createStore' in content:
                        stores_analysis["store_types"].append({
                            "file": str(store_file.relative_to(self.project_root)),
                            "type": "zustand"
                        })
                    elif 'createSlice' in content or '@reduxjs/toolkit' in content:
                        stores_analysis["store_types"].append({
                            "file": str(store_file.relative_to(self.project_root)),
                            "type": "redux_toolkit"
                        })
                    
                except Exception as e:
                    self.logger.warning(f"Error analyzing store {store_file}: {e}")
        
        except Exception as e:
            self.logger.error(f"Error analyzing stores: {e}")
        
        return stores_analysis
    
    async def _generate_state_recommendations(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate recommendations based on state analysis."""
        recommendations = []
        
        # Check state library usage
        state_libs = analysis.get("state_libraries", [])
        if not state_libs:
            recommendations.append({
                "type": "architecture",
                "priority": "medium",
                "title": "No formal state management detected",
                "description": "Consider implementing a state management solution like Zustand or Redux Toolkit",
                "implementation": "Add a state management library for complex state logic"
            })
        
        # Check component state patterns
        component_analysis = analysis.get("component_analysis", {})
        state_patterns = component_analysis.get("state_patterns", {})
        
        total_useState = state_patterns.get("useState", 0)
        if total_useState > 20:
            recommendations.append({
                "type": "performance",
                "priority": "high",
                "title": "High useState usage detected",
                "description": f"Found {total_useState} useState calls. Consider consolidating state or using useReducer",
                "implementation": "Refactor multiple useState calls into useReducer or global state"
            })
        
        # Check performance issues
        perf_issues = component_analysis.get("performance_issues", [])
        if perf_issues:
            recommendations.append({
                "type": "performance",
                "priority": "medium",
                "title": f"Performance optimization opportunities found",
                "description": f"Found {len(perf_issues)} potential performance issues",
                "implementation": "Review and optimize component rendering patterns"
            })
        
        return recommendations
    
    async def performance_optimization(self) -> Dict[str, Any]:
        """Optimize UI state management for performance."""
        self.logger.info("⚡ Optimizing UI state management performance...")
        
        # First analyze current state
        analysis = await self.analyze_state_architecture()
        
        optimizations = {
            "memoization_opportunities": [],
            "state_consolidation": [],
            "hook_optimizations": [],
            "render_optimizations": []
        }
        
        # Analyze for specific optimizations
        component_analysis = analysis.get("analysis", {}).get("component_analysis", {})
        
        # Look for memoization opportunities
        if component_analysis.get("performance_issues"):
            for issue in component_analysis["performance_issues"]:
                if "React.memo" in issue.get("issue", ""):
                    optimizations["memoization_opportunities"].append(issue)
                elif "inline" in issue.get("issue", "").lower():
                    optimizations["render_optimizations"].append(issue)
        
        return {
            "success": True,
            "optimizations": optimizations,
            "baseline_analysis": analysis,
            "timestamp": datetime.now().isoformat()
        }
    
    async def migration_planning(self) -> Dict[str, Any]:
        """Plan state management migration strategy."""
        self.logger.info("📋 Planning state management migration...")
        
        from_pattern = self.parameters.get("from_pattern", "useState")
        to_pattern = self.parameters.get("to_pattern", "zustand")
        
        migration_plan = {
            "from_pattern": from_pattern,
            "to_pattern": to_pattern,
            "migration_steps": [],
            "estimated_effort": "medium",
            "risk_assessment": "low",
            "rollback_plan": []
        }
        
        # Generate migration steps based on patterns
        if from_pattern == "useState" and to_pattern == "zustand":
            migration_plan["migration_steps"] = [
                "1. Install Zustand: npm install zustand",
                "2. Create global stores for shared state",
                "3. Identify components with complex local state",
                "4. Migrate component state to Zustand stores",
                "5. Replace useState with useStore hooks",
                "6. Test component functionality",
                "7. Remove unused useState calls"
            ]
            migration_plan["estimated_effort"] = "medium"
        
        elif from_pattern == "redux" and to_pattern == "zustand":
            migration_plan["migration_steps"] = [
                "1. Install Zustand and analyze Redux stores",
                "2. Create equivalent Zustand stores",
                "3. Migrate reducers to Zustand actions",
                "4. Replace useSelector with useStore",
                "5. Remove Redux provider and store setup",
                "6. Update TypeScript types",
                "7. Test all state interactions"
            ]
            migration_plan["estimated_effort"] = "high"
        
        return {
            "success": True,
            "migration_plan": migration_plan,
            "timestamp": datetime.now().isoformat()
        }
    
    async def state_synchronization_audit(self) -> Dict[str, Any]:
        """Audit state synchronization between frontend and backend."""
        self.logger.info("🔄 Auditing state synchronization...")
        
        sync_analysis = {
            "api_calls": 0,
            "state_mutations": 0,
            "sync_patterns": [],
            "issues": [],
            "recommendations": []
        }
        
        # This would analyze API calls and state updates
        # For now, provide basic structure
        sync_analysis["recommendations"].append({
            "type": "sync",
            "title": "Implement optimistic updates",
            "description": "Consider optimistic updates for better UX",
            "priority": "medium"
        })
        
        return {
            "success": True,
            "synchronization_audit": sync_analysis,
            "timestamp": datetime.now().isoformat()
        }
    
    async def default_state_analysis(self) -> Dict[str, Any]:
        """Default comprehensive state analysis."""
        return await self.analyze_state_architecture()

# Entry point for orchestration system
async def execute(context):
    """Execute function required by the orchestration system."""
    agent = UIStateManagerAgent(context)
    return await agent.execute_task()

if __name__ == "__main__":
    # Test execution
    test_context = {
        "agent": {"name": "ui-state-manager"},
        "task": {"name": "analyze_state_architecture", "parameters": {}},
        "config": {"project_root": str(Path(__file__).parent.parent.parent)},
        "knowledge_bases": {}
    }
    
    import asyncio
    result = asyncio.run(execute(test_context))
    print(json.dumps(result, indent=2))
