#!/usr/bin/env python3
"""
Workflow Orchestrator Agent
Advanced workflow management and ComfyUI integration specialist

This agent handles:
1. ComfyUI workflow creation and optimization
2. Node graph analysis and performance tuning
3. Custom node integration and validation
4. Workflow execution monitoring and error handling
5. Dynamic workflow generation based on requirements
"""

import asyncio
import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import requests
import time

# Import base agent functionality
sys.path.insert(0, str(Path(__file__).parent))
from base_agent import BaseAgent

class WorkflowOrchestratorAgent(BaseAgent):
    """
    Workflow Orchestrator Agent implementation.
    
    Manages ComfyUI workflows, node optimization, and execution monitoring.
    Provides intelligent workflow creation and performance optimization.
    """
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.project_root = Path(context['config']['project_root'])
        
        # ComfyUI configuration
        self.comfyui_url = context.get('config', {}).get('comfyui_url', 'http://localhost:8188')
        self.workflows_path = self.project_root / "workflows"
        self.custom_nodes_path = self.project_root / "ComfyUI_windows_portable" / "ComfyUI" / "custom_nodes"
        
        # Model directories for file verification
        self.model_directories = {
            "unet": Path("L:/ComfyUI/models/unet"),
            "checkpoints": Path("L:/ComfyUI/models/checkpoints"),
            "diffusion_models": Path("L:/ComfyUI/models/diffusion_models"),
            "vae": Path("L:/ComfyUI/models/vae"),
            "clip": Path("L:/ComfyUI/models/clip"),
            "text_encoders": Path("L:/ComfyUI/models/text_encoders"),
            "style_models": Path("L:/ComfyUI/models/style_models")
        }
        
        # Expected model file mappings (downloaded name -> expected name)
        self.model_file_mappings = {
            # FLUX Models
            "flux1-dev.safetensors": "flux1-dev.safetensors",
            "flux1-schnell.safetensors": "flux1-schnell.safetensors",
            "flux1-redux-dev.safetensors": "flux1-redux-dev.safetensors",  # FLUX Redux style model
            # VAE Models with obvious names
            "Flux_vae.safetensors": "Flux_vae.safetensors",             # FLUX VAE (keep original name)
            "ae.safetensors": "Flux_vae.safetensors",                   # Map ae.safetensors to FLUX VAE
            "hidream_i1_dev_fp8.safetensors": "hidream_vae.safetensors", # HiDream VAE (when in vae directory)
            "sdxl_vae.safetensors": "sdxl_vae.safetensors",             # SDXL VAE
            "wan_2.1_vae.safetensors": "wan_vae.safetensors",           # WAN VAE
            # Text Encoders with obvious names
            "t5xxl_fp16.safetensors": "t5xxl_fp16.safetensors",         # T5 text encoder
            "t5xxl_fp8_e4m3fn.safetensors": "t5xxl_fp8_e4m3fn.safetensors", # T5 FP8 text encoder
            "google_t5-v1_1-xxl_encoderonly-fp16.safetensors": "google_t5-v1_1-xxl_encoderonly-fp16.safetensors", # Improved Google T5 model
            "clip_l.safetensors": "clip_l.safetensors",                 # CLIP-L text encoder
            "clip_l_hidream.safetensors": "clip_l_hidream.safetensors", # CLIP-L HiDream
            "clip_g_hidream.safetensors": "clip_g_hidream.safetensors", # CLIP-G HiDream
            "llama_3.1_8b_instruct_fp8_scaled.safetensors": "llama_3.1_8b_instruct_fp8_scaled.safetensors", # Llama text encoder
            "flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors": "flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors"
        }
        
        # Default workflow parameters
        self.workflow_defaults = {
            "batch_size": 1,
            "steps": 20,
            "guidance": 3.5,
            "width": 1024,
            "height": 1024,
            "scheduler": "simple",
            "sampler": "euler"
        }
        
        # Preferred model selections (enhanced versions when available)
        self.preferred_models = {
            "flux": {
                "text_encoder_primary": "google_t5-v1_1-xxl_encoderonly-fp16.safetensors",  # Enhanced T5
                "text_encoder_secondary": "flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors",  # Enhanced FLUX CLIP
                "vae": "Flux_vae.safetensors",
                "unet": "flux1-dev.safetensors"
            },
            "hidream": {
                "text_encoder_primary": "clip_g_hidream.safetensors",
                "text_encoder_secondary": "clip_l_hidream.safetensors", 
                "vae": "hidream_vae.safetensors"
            },
            "sdxl": {
                "text_encoder": "clip_l.safetensors",
                "vae": "sdxl_vae.safetensors"
            }
        }
        
        # Workflow templates and configs
        self.workflow_templates = {}
        self.node_registry = {}
        
    async def execute_task(self) -> Dict[str, Any]:
        """Execute the specific workflow orchestrator task."""
        task_name = self.task_name
        
        if task_name == "analyze_workflows":
            return await self.analyze_workflows()
        elif task_name == "optimize_workflow":
            return await self.optimize_workflow()
        elif task_name == "create_workflow":
            return await self.create_workflow()
        elif task_name == "validate_nodes":
            return await self.validate_nodes()
        elif task_name == "validate_workflows":
            return await self.analyze_workflows()  # Alias for analyze_workflows
        elif task_name == "monitor_execution":
            return await self.monitor_execution()
        elif task_name == "benchmark_performance":
            return await self.benchmark_performance()
        else:
            return await self.default_workflow_analysis()
    
    async def analyze_workflows(self) -> Dict[str, Any]:
        """Analyze existing workflows for optimization opportunities."""
        self.logger.info("🔍 Analyzing ComfyUI workflows...")
        
        analysis_results = {
            "total_workflows": 0,
            "workflow_categories": {},
            "node_usage_stats": {},
            "performance_metrics": {},
            "optimization_opportunities": [],
            "error_patterns": []
        }
        
        # Check ComfyUI connectivity
        comfyui_status = await self._check_comfyui_connection()
        analysis_results["comfyui_connected"] = comfyui_status
        
        if not comfyui_status:
            self.logger.warning("ComfyUI not accessible, analyzing offline workflows only")
        
        # Analyze workflow files
        if self.workflows_path.exists():
            workflow_analysis = await self._analyze_workflow_files()
            analysis_results.update(workflow_analysis)
        
        # Analyze custom nodes
        if self.custom_nodes_path.exists():
            nodes_analysis = await self._analyze_custom_nodes()
            analysis_results["custom_nodes"] = nodes_analysis
        
        # Get system performance metrics if ComfyUI is available
        if comfyui_status:
            system_info = await self._get_system_info()
            analysis_results["system_info"] = system_info
        
        # Generate optimization recommendations
        optimizations = await self._generate_optimization_recommendations(analysis_results)
        analysis_results["optimization_opportunities"] = optimizations
        
        return {
            "success": True,
            "analysis": analysis_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _check_comfyui_connection(self) -> bool:
        """Check if ComfyUI is accessible."""
        try:
            response = requests.get(f"{self.comfyui_url}/system_stats", timeout=5)
            return response.status_code == 200
        except Exception as e:
            self.logger.debug(f"ComfyUI connection check failed: {e}")
            return False
    
    async def _analyze_workflow_files(self) -> Dict[str, Any]:
        """Analyze workflow JSON files."""
        workflow_analysis = {
            "total_workflows": 0,
            "workflow_categories": {},
            "node_usage_stats": {},
            "complexity_scores": [],
            "workflow_details": []
        }
        
        try:
            # Find all workflow JSON files
            workflow_files = list(self.workflows_path.glob("**/*.json"))
            workflow_analysis["total_workflows"] = len(workflow_files)
            
            for workflow_file in workflow_files:
                try:
                    with open(workflow_file, 'r') as f:
                        workflow_data = json.load(f)
                    
                    # Analyze workflow structure
                    workflow_info = await self._analyze_single_workflow(workflow_file, workflow_data)
                    workflow_analysis["workflow_details"].append(workflow_info)
                    
                    # Update node usage statistics
                    for node_type in workflow_info.get("node_types", []):
                        if node_type in workflow_analysis["node_usage_stats"]:
                            workflow_analysis["node_usage_stats"][node_type] += 1
                        else:
                            workflow_analysis["node_usage_stats"][node_type] = 1
                    
                    # Categorize workflow
                    category = workflow_info.get("category", "uncategorized")
                    if category in workflow_analysis["workflow_categories"]:
                        workflow_analysis["workflow_categories"][category] += 1
                    else:
                        workflow_analysis["workflow_categories"][category] = 1
                    
                    # Track complexity
                    complexity = workflow_info.get("complexity_score", 0)
                    workflow_analysis["complexity_scores"].append(complexity)
                    
                except Exception as e:
                    self.logger.warning(f"Error analyzing workflow {workflow_file}: {e}")
        
        except Exception as e:
            self.logger.error(f"Error analyzing workflow files: {e}")
        
        return workflow_analysis
    
    async def _analyze_single_workflow(self, file_path: Path, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a single workflow file."""
        workflow_info = {
            "name": file_path.stem,
            "path": str(file_path.relative_to(self.project_root)),
            "node_count": 0,
            "node_types": [],
            "complexity_score": 0,
            "category": "uncategorized",
            "inputs": [],
            "outputs": [],
            "performance_bottlenecks": []
        }
        
        # Count nodes and analyze structure
        if isinstance(workflow_data, dict):
            nodes = workflow_data.get("nodes", [])
            if isinstance(nodes, list):
                workflow_info["node_count"] = len(nodes)
                
                for node in nodes:
                    if isinstance(node, dict):
                        node_type = node.get("type", "unknown")
                        workflow_info["node_types"].append(node_type)
                        
                        # Identify inputs and outputs
                        if "input" in node_type.lower() or "load" in node_type.lower():
                            workflow_info["inputs"].append(node_type)
                        elif "output" in node_type.lower() or "save" in node_type.lower():
                            workflow_info["outputs"].append(node_type)
                        
                        # Check for potential bottlenecks
                        if any(keyword in node_type.lower() for keyword in ["upscale", "denoise", "generate"]):
                            workflow_info["performance_bottlenecks"].append(node_type)
        
        # Calculate complexity score
        workflow_info["complexity_score"] = self._calculate_complexity_score(workflow_info)
        
        # Determine category
        workflow_info["category"] = self._categorize_workflow(workflow_info)
        
        return workflow_info
    
    def _calculate_complexity_score(self, workflow_info: Dict[str, Any]) -> int:
        """Calculate workflow complexity score."""
        base_score = workflow_info["node_count"]
        
        # Add complexity for certain node types
        complex_nodes = ["KSampler", "ControlNet", "LoRA", "Upscaler"]
        for node_type in workflow_info["node_types"]:
            if any(complex_type in node_type for complex_type in complex_nodes):
                base_score += 5
        
        # Add complexity for multiple inputs/outputs
        base_score += len(workflow_info["inputs"]) * 2
        base_score += len(workflow_info["outputs"]) * 2
        
        return base_score
    
    def _categorize_workflow(self, workflow_info: Dict[str, Any]) -> str:
        """Categorize workflow based on node types."""
        node_types = workflow_info["node_types"]
        
        # Check for common workflow patterns
        if any("txt2img" in node.lower() for node in node_types):
            return "text_to_image"
        elif any("img2img" in node.lower() for node in node_types):
            return "image_to_image"
        elif any("controlnet" in node.lower() for node in node_types):
            return "controlnet"
        elif any("upscale" in node.lower() for node in node_types):
            return "upscaling"
        elif any("inpaint" in node.lower() for node in node_types):
            return "inpainting"
        else:
            return "general"
    
    async def _analyze_custom_nodes(self) -> Dict[str, Any]:
        """Analyze installed custom nodes."""
        nodes_analysis = {
            "total_custom_nodes": 0,
            "node_categories": {},
            "installation_status": {},
            "compatibility_issues": [],
            "update_recommendations": []
        }
        
        try:
            if not self.custom_nodes_path.exists():
                return nodes_analysis
            
            # Count custom node directories
            custom_node_dirs = [d for d in self.custom_nodes_path.iterdir() if d.is_dir()]
            nodes_analysis["total_custom_nodes"] = len(custom_node_dirs)
            
            for node_dir in custom_node_dirs:
                try:
                    # Check for common files that indicate node type
                    node_info = await self._analyze_custom_node_dir(node_dir)
                    
                    category = node_info.get("category", "unknown")
                    if category in nodes_analysis["node_categories"]:
                        nodes_analysis["node_categories"][category] += 1
                    else:
                        nodes_analysis["node_categories"][category] = 1
                    
                    # Check installation status
                    if node_info.get("has_requirements"):
                        nodes_analysis["installation_status"][node_dir.name] = "requires_dependencies"
                    else:
                        nodes_analysis["installation_status"][node_dir.name] = "ready"
                
                except Exception as e:
                    self.logger.warning(f"Error analyzing custom node {node_dir}: {e}")
        
        except Exception as e:
            self.logger.error(f"Error analyzing custom nodes: {e}")
        
        return nodes_analysis
    
    async def _analyze_custom_node_dir(self, node_dir: Path) -> Dict[str, Any]:
        """Analyze a single custom node directory."""
        node_info = {
            "name": node_dir.name,
            "has_requirements": False,
            "has_install_script": False,
            "category": "unknown",
            "files": []
        }
        
        # Check for common files
        requirements_file = node_dir / "requirements.txt"
        install_script = node_dir / "install.py"
        init_file = node_dir / "__init__.py"
        
        node_info["has_requirements"] = requirements_file.exists()
        node_info["has_install_script"] = install_script.exists()
        
        # List Python files
        python_files = list(node_dir.glob("*.py"))
        node_info["files"] = [f.name for f in python_files]
        
        # Try to categorize based on name and files
        name_lower = node_dir.name.lower()
        if any(keyword in name_lower for keyword in ["controlnet", "control"]):
            node_info["category"] = "controlnet"
        elif any(keyword in name_lower for keyword in ["upscal", "super", "resolution"]):
            node_info["category"] = "upscaling"
        elif any(keyword in name_lower for keyword in ["lora", "embedding"]):
            node_info["category"] = "model_enhancement"
        elif any(keyword in name_lower for keyword in ["face", "detect", "segment"]):
            node_info["category"] = "detection"
        else:
            node_info["category"] = "utility"
        
        return node_info
    
    async def _get_system_info(self) -> Dict[str, Any]:
        """Get system information from ComfyUI."""
        system_info = {}
        
        try:
            # Get system stats
            response = requests.get(f"{self.comfyui_url}/system_stats")
            if response.status_code == 200:
                system_info = response.json()
            
            # Get device info
            device_response = requests.get(f"{self.comfyui_url}/device_info")
            if device_response.status_code == 200:
                system_info["device_info"] = device_response.json()
        
        except Exception as e:
            self.logger.warning(f"Could not retrieve system info: {e}")
        
        return system_info
    
    async def _generate_optimization_recommendations(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate workflow optimization recommendations."""
        recommendations = []
        
        # Check complexity scores
        complexity_scores = analysis.get("complexity_scores", [])
        if complexity_scores:
            avg_complexity = sum(complexity_scores) / len(complexity_scores)
            if avg_complexity > 50:
                recommendations.append({
                    "type": "performance",
                    "priority": "high",
                    "title": "High workflow complexity detected",
                    "description": f"Average complexity score: {avg_complexity:.1f}. Consider simplifying workflows.",
                    "implementation": "Split complex workflows into smaller, reusable components"
                })
        
        # Check node usage patterns
        node_stats = analysis.get("node_usage_stats", {})
        if node_stats:
            # Check for potentially slow nodes
            slow_nodes = ["KSampler", "VAEDecode", "UNetLoader"]
            for slow_node in slow_nodes:
                if any(slow_node in node_type for node_type in node_stats.keys()):
                    count = sum(1 for node_type in node_stats.keys() if slow_node in node_type)
                    if count > 3:
                        recommendations.append({
                            "type": "performance",
                            "priority": "medium",
                            "title": f"Multiple {slow_node} nodes detected",
                            "description": f"Found {count} instances. Consider caching or optimization.",
                            "implementation": f"Implement {slow_node} result caching or reduce usage"
                        })
        
        # Check custom nodes
        custom_nodes = analysis.get("custom_nodes", {})
        total_custom = custom_nodes.get("total_custom_nodes", 0)
        if total_custom > 20:
            recommendations.append({
                "type": "maintenance",
                "priority": "medium",
                "title": "Many custom nodes installed",
                "description": f"Found {total_custom} custom nodes. Review for conflicts.",
                "implementation": "Audit custom nodes for conflicts and update dependencies"
            })
        
        return recommendations
    
    async def optimize_workflow(self) -> Dict[str, Any]:
        """Optimize a specific workflow."""
        self.logger.info("⚡ Optimizing workflow...")
        
        workflow_path = self.parameters.get("workflow_path")
        if not workflow_path:
            return {"success": False, "error": "No workflow path specified"}
        
        optimizations = {
            "original_complexity": 0,
            "optimized_complexity": 0,
            "changes_made": [],
            "performance_impact": "unknown",
            "recommendations": []
        }
        
        try:
            # Load and analyze workflow
            with open(workflow_path, 'r') as f:
                workflow_data = json.load(f)
            
            # Calculate original complexity
            workflow_info = await self._analyze_single_workflow(Path(workflow_path), workflow_data)
            optimizations["original_complexity"] = workflow_info["complexity_score"]
            
            # Apply optimizations
            optimized_workflow = await self._apply_workflow_optimizations(workflow_data)
            
            # Calculate new complexity
            optimized_info = await self._analyze_single_workflow(Path(workflow_path), optimized_workflow)
            optimizations["optimized_complexity"] = optimized_info["complexity_score"]
            
            # Save optimized workflow
            optimized_path = workflow_path.replace(".json", "_optimized.json")
            with open(optimized_path, 'w') as f:
                json.dump(optimized_workflow, f, indent=2)
            
            optimizations["optimized_workflow_path"] = optimized_path
            
        except Exception as e:
            self.logger.error(f"Error optimizing workflow: {e}")
            return {"success": False, "error": str(e)}
        
        return {
            "success": True,
            "optimizations": optimizations,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _apply_workflow_optimizations(self, workflow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply specific optimizations to workflow."""
        # Detect workflow type
        workflow_type = self._detect_workflow_type(workflow_data)
        
        # Apply model preferences and defaults
        optimized_workflow = self.update_workflow_with_preferences(workflow_data, workflow_type)
        
        # Additional optimizations:
        # 1. Remove redundant nodes
        # 2. Optimize node parameters  
        # 3. Reorganize node connections
        # 4. Cache intermediate results
        # (These would be implemented based on specific optimization needs)
        
        return optimized_workflow
    
    def _detect_workflow_type(self, workflow_data: Dict[str, Any]) -> str:
        """Detect the workflow type based on nodes and parameters."""
        if "nodes" not in workflow_data:
            return "unknown"
        
        # Check for FLUX-specific patterns
        for node in workflow_data["nodes"]:
            node_type = node.get("type", "")
            params = node.get("params", {})
            
            if node_type == "DualCLIPLoader" and params.get("type") == "flux":
                return "flux"
            elif node_type == "UNETLoader" and "flux" in params.get("unet_name", "").lower():
                return "flux"
            elif "hidream" in str(params).lower():
                return "hidream"
            elif "sdxl" in str(params).lower():
                return "sdxl"
        
        return "basic"
    
    async def create_workflow(self) -> Dict[str, Any]:
        """Create a new workflow based on parameters."""
        self.logger.info("🔧 Creating new workflow...")
        
        workflow_type = self.parameters.get("type", "basic")
        workflow_name = self.parameters.get("name", f"generated_workflow_{int(time.time())}")
        
        # Generate workflow based on type
        if workflow_type == "txt2img":
            workflow_data = await self._create_txt2img_workflow()
        elif workflow_type == "img2img":
            workflow_data = await self._create_img2img_workflow()
        elif workflow_type == "controlnet":
            workflow_data = await self._create_controlnet_workflow()
        else:
            workflow_data = await self._create_basic_workflow()
        
        # Save workflow
        workflow_path = self.workflows_path / f"{workflow_name}.json"
        self.workflows_path.mkdir(exist_ok=True)
        
        with open(workflow_path, 'w') as f:
            json.dump(workflow_data, f, indent=2)
        
        return {
            "success": True,
            "workflow_path": str(workflow_path),
            "workflow_type": workflow_type,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _create_txt2img_workflow(self) -> Dict[str, Any]:
        """Create a basic text-to-image workflow."""
        return {
            "nodes": [
                {"id": 1, "type": "CLIPTextEncode", "inputs": {"text": "masterpiece, best quality"}},
                {"id": 2, "type": "CheckpointLoaderSimple", "inputs": {}},
                {"id": 3, "type": "KSampler", "inputs": {"steps": 20, "cfg": 7.0}},
                {"id": 4, "type": "VAEDecode", "inputs": {}},
                {"id": 5, "type": "SaveImage", "inputs": {"filename_prefix": "ComfyUI"}}
            ],
            "connections": [
                {"from": 1, "to": 3},
                {"from": 2, "to": 3},
                {"from": 3, "to": 4},
                {"from": 4, "to": 5}
            ]
        }
    
    async def _create_img2img_workflow(self) -> Dict[str, Any]:
        """Create a basic image-to-image workflow."""
        return {
            "nodes": [
                {"id": 1, "type": "LoadImage", "inputs": {}},
                {"id": 2, "type": "CLIPTextEncode", "inputs": {"text": "masterpiece, best quality"}},
                {"id": 3, "type": "CheckpointLoaderSimple", "inputs": {}},
                {"id": 4, "type": "KSampler", "inputs": {"steps": 20, "cfg": 7.0, "denoise": 0.7}},
                {"id": 5, "type": "VAEDecode", "inputs": {}},
                {"id": 6, "type": "SaveImage", "inputs": {"filename_prefix": "ComfyUI"}}
            ]
        }
    
    async def _create_controlnet_workflow(self) -> Dict[str, Any]:
        """Create a basic ControlNet workflow."""
        return {
            "nodes": [
                {"id": 1, "type": "LoadImage", "inputs": {}},
                {"id": 2, "type": "ControlNetLoader", "inputs": {}},
                {"id": 3, "type": "CLIPTextEncode", "inputs": {"text": "masterpiece, best quality"}},
                {"id": 4, "type": "CheckpointLoaderSimple", "inputs": {}},
                {"id": 5, "type": "KSampler", "inputs": {"steps": 20, "cfg": 7.0}},
                {"id": 6, "type": "VAEDecode", "inputs": {}},
                {"id": 7, "type": "SaveImage", "inputs": {"filename_prefix": "ComfyUI"}}
            ]
        }
    
    async def _create_basic_workflow(self) -> Dict[str, Any]:
        """Create a basic workflow template."""
        return await self._create_txt2img_workflow()
    
    async def validate_nodes(self) -> Dict[str, Any]:
        """Validate custom nodes and dependencies."""
        self.logger.info("✅ Validating nodes and dependencies...")
        
        validation_results = {
            "valid_nodes": [],
            "invalid_nodes": [],
            "missing_dependencies": [],
            "compatibility_issues": [],
            "recommendations": []
        }
        
        # This would implement actual node validation
        # For now, return basic structure
        return {
            "success": True,
            "validation": validation_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def monitor_execution(self) -> Dict[str, Any]:
        """Monitor workflow execution."""
        self.logger.info("📊 Monitoring workflow execution...")
        
        monitoring_data = {
            "active_workflows": 0,
            "queue_status": {},
            "performance_metrics": {},
            "error_log": []
        }
        
        # This would implement actual execution monitoring
        return {
            "success": True,
            "monitoring": monitoring_data,
            "timestamp": datetime.now().isoformat()
        }
    
    async def benchmark_performance(self) -> Dict[str, Any]:
        """Benchmark workflow performance."""
        self.logger.info("🚀 Benchmarking workflow performance...")
        
        benchmark_results = {
            "execution_times": {},
            "memory_usage": {},
            "gpu_utilization": {},
            "bottlenecks": [],
            "optimization_suggestions": []
        }
        
        # This would implement actual performance benchmarking
        return {
            "success": True,
            "benchmark": benchmark_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def default_workflow_analysis(self) -> Dict[str, Any]:
        """Default comprehensive workflow analysis."""
        self.logger.info("🔍 Performing comprehensive workflow analysis...")
        
        # First, verify and fix model file names
        model_verification = await self._verify_model_files()
        
        # Then perform regular workflow analysis
        analysis_result = await self.analyze_workflows()
        
        # Add model verification results
        analysis_result["model_verification"] = model_verification
        
        return analysis_result
    
    async def _verify_model_files(self) -> Dict[str, Any]:
        """Verify and fix model file names from L: directory."""
        self.logger.info("🔍 Verifying model file names from L: directory...")
        
        verification_results = {
            "files_checked": 0,
            "files_renamed": 0,
            "missing_files": [],
            "incorrect_names": [],
            "verification_errors": [],
            "model_inventory": {}
        }
        
        for model_type, model_dir in self.model_directories.items():
            if not model_dir.exists():
                verification_results["verification_errors"].append(
                    f"Model directory not found: {model_dir}"
                )
                continue
                
            self.logger.info(f"Checking {model_type} directory: {model_dir}")
            verification_results["model_inventory"][model_type] = []
            
            try:
                # Get all files in the directory and subdirectories
                model_files = list(model_dir.rglob("*.safetensors")) + list(model_dir.rglob("*.gguf"))
                verification_results["files_checked"] += len(model_files)
                
                for file_path in model_files:
                    file_info = {
                        "original_name": file_path.name,
                        "size_mb": round(file_path.stat().st_size / (1024 * 1024), 1),
                        "status": "verified"
                    }
                    
                    # Check if file name matches expected patterns
                    expected_name = self._get_expected_filename(file_path.name, model_type)
                    
                    if expected_name and expected_name != file_path.name:
                        # File needs renaming
                        new_path = file_path.parent / expected_name
                        
                        if not new_path.exists():
                            try:
                                self.logger.info(f"Renaming {file_path.name} -> {expected_name}")
                                file_path.rename(new_path)
                                verification_results["files_renamed"] += 1
                                file_info["renamed_to"] = expected_name
                                file_info["status"] = "renamed"
                            except Exception as e:
                                verification_results["verification_errors"].append(
                                    f"Failed to rename {file_path.name}: {e}"
                                )
                                file_info["status"] = "rename_failed"
                                file_info["error"] = str(e)
                        else:
                            file_info["status"] = "target_exists"
                            verification_results["incorrect_names"].append(file_path.name)
                    
                    verification_results["model_inventory"][model_type].append(file_info)
                    
            except Exception as e:
                verification_results["verification_errors"].append(
                    f"Error processing {model_type} directory: {e}"
                )
        
        # Check for critical missing files
        missing_critical = self._check_critical_files(verification_results["model_inventory"])
        verification_results["missing_critical_files"] = missing_critical
        
        self.logger.info(f"Model verification complete: {verification_results['files_renamed']} files renamed")
        return verification_results
    
    def _get_expected_filename(self, current_name: str, model_type: str) -> Optional[str]:
        """Get the expected filename for a model file."""
        # Direct mapping if exists (case-insensitive)
        for original_name, expected_name in self.model_file_mappings.items():
            if current_name.lower() == original_name.lower():
                return expected_name
        
        # Pattern-based corrections for common download naming issues
        name_lower = current_name.lower()
        
        # FLUX model corrections
        if "flux" in name_lower:
            if "redux" in name_lower:
                return "flux1-redux-dev.safetensors"
            elif "dev" in name_lower and "schnell" not in name_lower:
                return "flux1-dev.safetensors"
            elif "schnell" in name_lower:
                return "flux1-schnell.safetensors"
            elif "canny" in name_lower:
                return "flux1-canny-dev.safetensors"
        
        # Text encoder corrections  
        elif "t5xxl" in name_lower:
            if "fp16" in name_lower:
                return "t5xxl_fp16.safetensors"
            elif "fp8" in name_lower:
                return "t5xxl_fp8_e4m3fn.safetensors"
        elif "google" in name_lower and "t5" in name_lower and "xxl" in name_lower:
            return "google_t5-v1_1-xxl_encoderonly-fp16.safetensors"
        
        # CLIP corrections
        elif "clip" in name_lower and "l" in name_lower:
            if "flux" in name_lower and "vit" in name_lower:
                return "flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors"
            else:
                return "clip_l.safetensors"
        elif "vit" in name_lower and "flux" in name_lower and "text" in name_lower:
            return "flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors"
        
        # VAE corrections with obvious names
        elif name_lower in ["ae.safetensors", "vae.safetensors", "flux_vae.safetensors"]:
            return "Flux_vae.safetensors"  # Use actual FLUX VAE filename
        elif "flux" in name_lower and "vae" in name_lower:
            return "Flux_vae.safetensors"
        elif "hidream" in name_lower and "vae" in name_lower:
            return "hidream_vae.safetensors"
        elif "sdxl" in name_lower and "vae" in name_lower:
            return "sdxl_vae.safetensors"
        elif "wan" in name_lower and "vae" in name_lower:
            return "wan_vae.safetensors"
        
        # HiDream corrections
        elif "hidream" in name_lower:
            return "hidream_i1_dev_fp8.safetensors"
        
        # SDXL corrections
        elif "sdxl" in name_lower:
            if "base" in name_lower:
                return "sdxl_base_1.0.safetensors"
            elif "vae" in name_lower:
                return "sdxl_vae.safetensors"
        
        return None
    
    def get_preferred_models(self, workflow_type: str) -> Dict[str, str]:
        """Get preferred model selections for a workflow type."""
        return self.preferred_models.get(workflow_type, {})
    
    def get_workflow_defaults(self) -> Dict[str, Any]:
        """Get default workflow parameters."""
        return self.workflow_defaults.copy()
    
    def update_workflow_with_preferences(self, workflow_data: Dict[str, Any], workflow_type: str) -> Dict[str, Any]:
        """Update workflow data with preferred models and default parameters."""
        preferred = self.get_preferred_models(workflow_type)
        defaults = self.get_workflow_defaults()
        
        # Update nodes with preferred models
        if "nodes" in workflow_data:
            for node in workflow_data["nodes"]:
                node_type = node.get("type", "")
                params = node.get("params", {})
                
                # Update VAE loader
                if node_type == "VAELoader" and "vae" in preferred:
                    params["vae_name"] = preferred["vae"]
                
                # Update CLIP/Text encoder loaders
                elif node_type == "DualCLIPLoader" and workflow_type == "flux":
                    if "text_encoder_primary" in preferred:
                        params["clip_name1"] = preferred["text_encoder_primary"]
                    if "text_encoder_secondary" in preferred:
                        params["clip_name2"] = preferred["text_encoder_secondary"]
                
                # Update UNET loader
                elif node_type == "UNETLoader" and "unet" in preferred:
                    params["unet_name"] = preferred["unet"]
                
                # Update batch size and other defaults
                elif node_type == "EmptySD3LatentImage":
                    params["batch_size"] = defaults["batch_size"]
                    if "width" not in params:
                        params["width"] = defaults["width"]
                    if "height" not in params:
                        params["height"] = defaults["height"]
                
                # Update scheduler defaults
                elif node_type == "BasicScheduler":
                    if "steps" not in params:
                        params["steps"] = defaults["steps"]
                    if "scheduler" not in params:
                        params["scheduler"] = defaults["scheduler"]
                
                # Update guidance defaults
                elif node_type == "FluxGuidance":
                    if "guidance" not in params:
                        params["guidance"] = defaults["guidance"]
                
                # Update sampler defaults
                elif node_type == "KSamplerSelect":
                    if "sampler_name" not in params:
                        params["sampler_name"] = defaults["sampler"]
        
        return workflow_data

    def _check_critical_files(self, model_inventory: Dict[str, List]) -> List[str]:
        """Check for missing critical model files."""
        critical_files = {
            "unet": ["flux1-dev.safetensors", "flux1-schnell.safetensors"],
            "text_encoders": ["t5xxl_fp16.safetensors", "clip_l.safetensors", "google_t5-v1_1-xxl_encoderonly-fp16.safetensors"],
            "clip": ["flux-ViT-L-14-TEXT-detail-improved-hiT-GmP-TE-only-HF.safetensors", "clip_l.safetensors"],
            "vae": ["Flux_vae.safetensors", "hidream_vae.safetensors", "sdxl_vae.safetensors", "wan_vae.safetensors"],  # Use actual file names
            "style_models": ["flux1-redux-dev.safetensors"],  # FLUX Redux style model
            "diffusion_models": ["hidream_i1_dev_fp8.safetensors"]
        }
        
        missing_files = []
        
        for model_type, required_files in critical_files.items():
            if model_type in model_inventory:
                existing_files = [f["original_name"] for f in model_inventory[model_type]]
                renamed_files = [f.get("renamed_to", f["original_name"]) for f in model_inventory[model_type]]
                all_files = existing_files + renamed_files
                
                for required_file in required_files:
                    if required_file not in all_files:
                        missing_files.append(f"{model_type}/{required_file}")
        
        return missing_files

# Entry point for orchestration system
async def execute(context):
    """Execute function required by the orchestration system."""
    agent = WorkflowOrchestratorAgent(context)
    return await agent.execute_task()

if __name__ == "__main__":
    # Test execution
    test_context = {
        "agent": {"name": "workflow-orchestrator"},
        "task": {"name": "analyze_workflows", "parameters": {}},
        "config": {"project_root": str(Path(__file__).parent.parent.parent)},
        "knowledge_bases": {}
    }
    
    import asyncio
    result = asyncio.run(execute(test_context))
    print(json.dumps(result, indent=2))
