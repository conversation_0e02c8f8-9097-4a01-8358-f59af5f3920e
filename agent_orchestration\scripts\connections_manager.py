#!/usr/bin/env python3
"""
Enhanced System Connections Manager with Startup Script Management
Responsible for maintaining functional startup scripts, port management, and system health
"""

import os
import sys
import json
import subprocess
import time
import socket
import psutil
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

@dataclass
class AgentResult:
    """Result object for agent operations"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    execution_time: Optional[float] = None

class BaseAgent:
    """Base class for all agents"""
    
    def __init__(self):
        self.agent_name = "base-agent"
        self.start_time = None
        
    def execute_task(self, task_name: str, parameters: Dict[str, Any]) -> AgentResult:
        """Execute a task - must be implemented by subclasses"""
        raise NotImplementedError("Subclasses must implement execute_task")
        
    def get_execution_time(self) -> float:
        """Get execution time in seconds"""
        if self.start_time is None:
            return 0.0
        return time.time() - self.start_time
        
    def start_timing(self):
        """Start execution timing"""
        self.start_time = time.time()
        
    def stop_timing(self) -> float:
        """Stop timing and return elapsed time"""
        if self.start_time is None:
            return 0.0
        elapsed = time.time() - self.start_time
        self.start_time = None
        return elapsed

class SystemConnectionsManager(BaseAgent):
    """Enhanced System Connections Manager with Startup Script Management"""
    
    def __init__(self):
        super().__init__()
        self.agent_name = "system-connections-manager"
        self.project_root = Path("G:/comfyui_Front")
        self.port_comfy_root = Path("G:/PORT_COMFY_front")
        
        # Service configuration
        self.services = {
            "backend": {
                "port": 8000,
                "venv": self.project_root / "backend" / "venv",
                "start_cmd": "uvicorn main:app --host 0.0.0.0 --port 8000",
                "working_dir": self.project_root / "backend",
                "health_endpoint": "http://localhost:8000/health"
            },
            "frontend": {
                "port": 3003,
                "venv": None,  # Node.js environment
                "start_cmd": "npm run dev",
                "working_dir": self.project_root / "frontend",
                "health_endpoint": "http://localhost:3003"
            },
            "comfyui": {
                "port": 8188,
                "venv": None,  # Portable installation
                "start_cmd": "run_nvidia_gpu.bat",
                "working_dir": self.port_comfy_root / "ComfyUI_windows_portable",
                "health_endpoint": "http://localhost:8188"
            }
        }
        
    def execute_task(self, task_name: str, parameters: Dict[str, Any]) -> AgentResult:
        """Execute the specified task with parameters"""
        try:
            if task_name == "manage_startup_scripts":
                return self._manage_startup_scripts(parameters)
            elif task_name == "validate_startup_scripts":
                return self._validate_startup_scripts(parameters)
            elif task_name == "repair_startup_scripts":
                return self._repair_startup_scripts(parameters)
            elif task_name == "cleanup_obsolete_scripts":
                return self._cleanup_obsolete_scripts(parameters)
            elif task_name == "diagnose_connections":
                return self._diagnose_connections(parameters)
            elif task_name == "integration_health_check":
                return self._integration_health_check(parameters)
            elif task_name == "optimize_performance":
                return self._optimize_performance(parameters)
            else:
                return AgentResult(
                    success=False,
                    message=f"Unknown task: {task_name}",
                    data={"error": f"Task {task_name} not implemented"}
                )
                
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Error executing {task_name}: {str(e)}",
                data={"error": str(e), "traceback": str(e)}
            )

    def _manage_startup_scripts(self, parameters: Dict[str, Any]) -> AgentResult:
        """Create and maintain functional startup scripts"""
        script_type = parameters.get("script_type", "complete_system")
        port_management = parameters.get("port_management", True)
        venv_management = parameters.get("venv_management", True)
        health_checks = parameters.get("health_checks", True)
        auto_browser_open = parameters.get("auto_browser_open", True)
        
        results = {
            "scripts_created": [],
            "scripts_updated": [],
            "validation_results": {},
            "recommendations": []
        }
        
        try:
            if script_type in ["complete_system", "custom"]:
                # Create complete system startup script
                script_content = self._generate_complete_startup_script(
                    port_management, venv_management, health_checks, auto_browser_open
                )
                script_path = self.project_root / "START_COMPLETE_SYSTEM_PORT_3003.bat"
                
                # Backup existing script if it exists
                if script_path.exists():
                    backup_path = script_path.with_suffix(f".backup_{int(time.time())}.bat")
                    shutil.copy2(script_path, backup_path)
                    results["scripts_updated"].append(str(script_path))
                else:
                    results["scripts_created"].append(str(script_path))
                
                # Write the new script
                with open(script_path, 'w', encoding='utf-8') as f:
                    f.write(script_content)
                
                # Make script executable
                os.chmod(script_path, 0o755)
                
            if script_type in ["backend_only", "custom"]:
                # Create backend-only startup script
                backend_script = self._generate_backend_startup_script(venv_management, health_checks)
                backend_path = self.project_root / "START_BACKEND_ONLY.bat"
                
                with open(backend_path, 'w', encoding='utf-8') as f:
                    f.write(backend_script)
                results["scripts_created"].append(str(backend_path))
                
            if script_type in ["frontend_only", "custom"]:
                # Create frontend-only startup script
                frontend_script = self._generate_frontend_startup_script(health_checks)
                frontend_path = self.project_root / "START_FRONTEND_ONLY.bat"
                
                with open(frontend_path, 'w', encoding='utf-8') as f:
                    f.write(frontend_script)
                results["scripts_created"].append(str(frontend_path))
                
            if script_type in ["comfyui_only", "custom"]:
                # Create ComfyUI-only startup script
                comfyui_script = self._generate_comfyui_startup_script(health_checks)
                comfyui_path = self.project_root / "START_COMFYUI_ONLY.bat"
                
                with open(comfyui_path, 'w', encoding='utf-8') as f:
                    f.write(comfyui_script)
                results["scripts_created"].append(str(comfyui_path))
            
            # Validate all created/updated scripts
            for script_path in results["scripts_created"] + results["scripts_updated"]:
                validation = self._validate_single_script(Path(script_path))
                results["validation_results"][script_path] = validation
                
            # Generate recommendations
            results["recommendations"] = self._generate_startup_recommendations()
            
            return AgentResult(
                success=True,
                message=f"Successfully managed startup scripts for {script_type}",
                data=results
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Failed to manage startup scripts: {str(e)}",
                data={"error": str(e), "partial_results": results}
            )

    def _generate_complete_startup_script(self, port_management: bool, venv_management: bool, 
                                        health_checks: bool, auto_browser_open: bool) -> str:
        """Generate a complete system startup script"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        script = """@echo off
echo ================================================================
echo   ComfyUI Frontend Complete System Startup
echo   Generated: """ + timestamp + """
echo   System Connections Manager - Startup Script
echo ================================================================
echo.

REM Set up environment variables
set PROJECT_ROOT=G:\\comfyui_Front
set COMFYUI_ROOT=G:\\PORT_COMFY_front\\ComfyUI_windows_portable
set BACKEND_VENV=%PROJECT_ROOT%\\backend\\venv
set FRONTEND_DIR=%PROJECT_ROOT%\\frontend

REM Color codes for output
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set BLUE=[94m
set RESET=[0m

echo %GREEN%[STARTUP]%RESET% Initializing Complete System Startup...
echo.

"""

        if port_management:
            script += """
REM ================================================================
REM Port Management and Conflict Resolution
REM ================================================================
echo %BLUE%[PORT CHECK]%RESET% Checking for port conflicts...

REM Function to check if port is in use
:check_port
netstat -an | findstr ":8000 " > nul
if %errorlevel% == 0 (
    echo %YELLOW%[WARNING]%RESET% Port 8000 is in use, attempting to free...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8000 "') do (
        echo Killing process %%a on port 8000
        taskkill /pid %%a /f > nul 2>&1
    )
)

netstat -an | findstr ":3003 " > nul
if %errorlevel% == 0 (
    echo %YELLOW%[WARNING]%RESET% Port 3003 is in use, attempting to free...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":3003 "') do (
        echo Killing process %%a on port 3003
        taskkill /pid %%a /f > nul 2>&1
    )
)

netstat -an | findstr ":8188 " > nul
if %errorlevel% == 0 (
    echo %YELLOW%[WARNING]%RESET% Port 8188 is in use, attempting to free...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":8188 "') do (
        echo Killing process %%a on port 8188
        taskkill /pid %%a /f > nul 2>&1
    )
)

echo %GREEN%[PORT CHECK]%RESET% Port conflict resolution completed
echo.

"""

        script += """
REM ================================================================
REM Start ComfyUI Service (Background)
REM ================================================================
echo %GREEN%[COMFYUI]%RESET% Starting ComfyUI service...

if not exist "%COMFYUI_ROOT%" (
    echo %RED%[ERROR]%RESET% ComfyUI directory not found: %COMFYUI_ROOT%
    echo Please check the ComfyUI installation path
    pause
    exit /b 1
)

cd /d "%COMFYUI_ROOT%"
echo Starting ComfyUI in background...
start "ComfyUI Service" cmd /c "run_nvidia_gpu.bat"

REM Wait for ComfyUI to initialize
echo Waiting for ComfyUI to start (30 seconds)...
timeout /t 30 /nobreak > nul

"""

        if venv_management:
            script += """
REM ================================================================
REM Backend Virtual Environment Setup and Startup
REM ================================================================
echo %GREEN%[BACKEND]%RESET% Setting up backend environment...

if not exist "%BACKEND_VENV%\\Scripts\\activate.bat" (
    echo %RED%[ERROR]%RESET% Backend virtual environment not found: %BACKEND_VENV%
    echo Please run the environment setup script first
    pause
    exit /b 1
)

echo Activating backend virtual environment...
call "%BACKEND_VENV%\\Scripts\\activate.bat"

REM Verify Python and PyTorch installation
echo Verifying Python environment...
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA Available: {torch.cuda.is_available()}')" 2>nul
if %errorlevel% neq 0 (
    echo %YELLOW%[WARNING]%RESET% PyTorch verification failed, but continuing...
)

cd /d "%PROJECT_ROOT%\\backend"
echo Starting FastAPI backend in background...
start "FastAPI Backend" cmd /c "call %BACKEND_VENV%\\Scripts\\activate.bat && uvicorn main:app --host 0.0.0.0 --port 8000"

REM Wait for backend to initialize
echo Waiting for backend to start (15 seconds)...
timeout /t 15 /nobreak > nul

"""
        else:
            script += """
REM ================================================================
REM Backend Startup (without venv management)
REM ================================================================
echo %GREEN%[BACKEND]%RESET% Starting backend service...

cd /d "%PROJECT_ROOT%\\backend"
echo Starting FastAPI backend in background...
start "FastAPI Backend" cmd /c "uvicorn main:app --host 0.0.0.0 --port 8000"

echo Waiting for backend to start (15 seconds)...
timeout /t 15 /nobreak > nul

"""

        script += """
REM ================================================================
REM Frontend Startup
REM ================================================================
echo %GREEN%[FRONTEND]%RESET% Starting frontend development server...

if not exist "%FRONTEND_DIR%\\package.json" (
    echo %RED%[ERROR]%RESET% Frontend directory not found: %FRONTEND_DIR%
    pause
    exit /b 1
)

cd /d "%FRONTEND_DIR%"
echo Starting Next.js frontend...
start "Next.js Frontend" cmd /c "npm run dev"

REM Wait for frontend to initialize
echo Waiting for frontend to start (20 seconds)...
timeout /t 20 /nobreak > nul

"""

        if health_checks:
            script += """
REM ================================================================
REM Health Checks and Service Verification
REM ================================================================
echo %GREEN%[HEALTH CHECK]%RESET% Verifying all services...

echo Checking ComfyUI (port 8188)...
curl -s http://localhost:8188 > nul 2>&1
if %errorlevel% == 0 (
    echo %GREEN%[SUCCESS]%RESET% ComfyUI service is responding
) else (
    echo %YELLOW%[WARNING]%RESET% ComfyUI service may still be starting...
)

echo Checking Backend API (port 8000)...
curl -s http://localhost:8000/health > nul 2>&1
if %errorlevel% == 0 (
    echo %GREEN%[SUCCESS]%RESET% Backend API is responding
) else (
    echo %YELLOW%[WARNING]%RESET% Backend API may still be starting...
)

echo Checking Frontend (port 3003)...
curl -s http://localhost:3003 > nul 2>&1
if %errorlevel% == 0 (
    echo %GREEN%[SUCCESS]%RESET% Frontend is responding
) else (
    echo %YELLOW%[WARNING]%RESET% Frontend may still be starting...
)

echo.
echo %GREEN%[SYSTEM]%RESET% All services started. System should be ready in 1-2 minutes.

"""

        if auto_browser_open:
            script += """
REM ================================================================
REM Open Browser and Complete Startup
REM ================================================================
echo %GREEN%[BROWSER]%RESET% Opening application in default browser...

timeout /t 5 /nobreak > nul
start http://localhost:3003

"""

        script += """
echo.
echo ================================================================
echo   ComfyUI Frontend System Startup Complete!
echo   
echo   Frontend:  http://localhost:3003
echo   Backend:   http://localhost:8000
echo   ComfyUI:   http://localhost:8188
echo   
echo   All services are running in background windows.
echo   Close this window or press any key to continue.
echo ================================================================
echo.

pause
"""

        return script

    def _generate_backend_startup_script(self, venv_management: bool, health_checks: bool) -> str:
        """Generate backend-only startup script"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        venv_activation = "call \"%BACKEND_VENV%\\Scripts\\activate.bat\"" if venv_management else ""
        
        return """@echo off
echo ================================================================
echo   Backend Service Startup
echo   Generated: """ + timestamp + """
echo ================================================================

set PROJECT_ROOT=G:\\comfyui_Front
set BACKEND_VENV=%PROJECT_ROOT%\\backend\\venv

""" + venv_activation + """

cd /d "%PROJECT_ROOT%\\backend"
echo Starting FastAPI backend...

uvicorn main:app --host 0.0.0.0 --port 8000

pause
"""

    def _generate_frontend_startup_script(self, health_checks: bool) -> str:
        """Generate frontend-only startup script"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return """@echo off
echo ================================================================
echo   Frontend Service Startup
echo   Generated: """ + timestamp + """
echo ================================================================

set PROJECT_ROOT=G:\\comfyui_Front

cd /d "%PROJECT_ROOT%\\frontend"
echo Starting Next.js frontend...

npm run dev

pause
"""

    def _generate_comfyui_startup_script(self, health_checks: bool) -> str:
        """Generate ComfyUI-only startup script"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        return """@echo off
echo ================================================================
echo   ComfyUI Service Startup
echo   Generated: """ + timestamp + """
echo ================================================================

set COMFYUI_ROOT=G:\\PORT_COMFY_front\\ComfyUI_windows_portable

cd /d "%COMFYUI_ROOT%"
echo Starting ComfyUI...

run_nvidia_gpu.bat

pause
"""

    def _validate_startup_scripts(self, parameters: Dict[str, Any]) -> AgentResult:
        """Validate all startup scripts for correctness"""
        test_execution = parameters.get("test_execution", False)
        check_dependencies = parameters.get("check_dependencies", True)
        port_availability = parameters.get("port_availability", True)
        
        results = {
            "validated_scripts": {},
            "issues_found": [],
            "recommendations": [],
            "port_status": {}
        }
        
        # Scripts to validate
        scripts_to_check = [
            self.project_root / "START_COMPLETE_SYSTEM_PORT_3003.bat",
            self.project_root / "START_BACKEND_ONLY.bat", 
            self.project_root / "START_FRONTEND_ONLY.bat",
            self.project_root / "START_COMFYUI_ONLY.bat"
        ]
        
        for script_path in scripts_to_check:
            if script_path.exists():
                validation = self._validate_single_script(script_path, check_dependencies, port_availability)
                results["validated_scripts"][str(script_path)] = validation
                
                if not validation["valid"]:
                    results["issues_found"].extend(validation["issues"])
            else:
                results["issues_found"].append(f"Missing script: {script_path}")
        
        if port_availability:
            results["port_status"] = self._check_port_availability()
            
        results["recommendations"] = self._generate_validation_recommendations(results)
        
        return AgentResult(
            success=len(results["issues_found"]) == 0,
            message=f"Validated {len(results['validated_scripts'])} startup scripts",
            data=results
        )

    def _validate_single_script(self, script_path: Path, check_dependencies: bool = True, 
                               port_availability: bool = True) -> Dict[str, Any]:
        """Validate a single startup script"""
        validation = {
            "valid": True,
            "issues": [],
            "warnings": [],
            "file_size": 0,
            "last_modified": None
        }
        
        try:
            # Basic file checks
            if not script_path.exists():
                validation["valid"] = False
                validation["issues"].append(f"Script does not exist: {script_path}")
                return validation
                
            stat = script_path.stat()
            validation["file_size"] = stat.st_size
            validation["last_modified"] = datetime.fromtimestamp(stat.st_mtime).isoformat()
            
            if stat.st_size == 0:
                validation["valid"] = False
                validation["issues"].append("Script file is empty")
                return validation
            
            # Read and analyze script content
            with open(script_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if check_dependencies:
                # Check for required paths
                required_paths = [
                    "G:\\comfyui_Front",
                    "G:\\PORT_COMFY_front\\ComfyUI_windows_portable"
                ]
                
                for path in required_paths:
                    if path in content and not Path(path).exists():
                        validation["issues"].append(f"Referenced path does not exist: {path}")
                        validation["valid"] = False
                        
                # Check for virtual environment references
                if "backend\\venv" in content:
                    venv_path = Path("G:/comfyui_Front/backend/venv")
                    if not venv_path.exists():
                        validation["issues"].append("Backend virtual environment not found")
                        validation["valid"] = False
                        
            # Check for common script patterns
            if "uvicorn" in content and "fastapi" not in content.lower():
                validation["warnings"].append("Script uses uvicorn but doesn't mention FastAPI")
                
            if "npm run dev" in content:
                package_json = Path("G:/comfyui_Front/frontend/package.json")
                if not package_json.exists():
                    validation["issues"].append("Frontend package.json not found")
                    validation["valid"] = False
                    
        except Exception as e:
            validation["valid"] = False
            validation["issues"].append(f"Error validating script: {str(e)}")
            
        return validation

    def _check_port_availability(self) -> Dict[str, Any]:
        """Check availability of required ports"""
        ports_to_check = [8000, 3003, 8188]
        port_status = {}
        
        for port in ports_to_check:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                    sock.settimeout(1)
                    result = sock.connect_ex(('localhost', port))
                    port_status[port] = {
                        "available": result != 0,
                        "in_use": result == 0,
                        "process": self._get_process_on_port(port) if result == 0 else None
                    }
            except Exception as e:
                port_status[port] = {
                    "available": False,
                    "in_use": False,
                    "error": str(e)
                }
                
        return port_status

    def _get_process_on_port(self, port: int) -> Optional[Dict[str, Any]]:
        """Get process information for a port"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port:
                    try:
                        process = psutil.Process(conn.pid)
                        return {
                            "pid": conn.pid,
                            "name": process.name(),
                            "cmdline": process.cmdline()
                        }
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        return {"pid": conn.pid, "name": "Unknown", "cmdline": []}
        except Exception:
            pass
        return None

    def _repair_startup_scripts(self, parameters: Dict[str, Any]) -> AgentResult:
        """Automatically repair broken startup scripts"""
        backup_existing = parameters.get("backup_existing", True)
        update_paths = parameters.get("update_paths", True)
        fix_venv_references = parameters.get("fix_venv_references", True)
        repair_port_configs = parameters.get("repair_port_configs", True)
        
        repairs_made = []
        issues_found = []
        
        try:
            # First validate to identify issues
            validation_result = self._validate_startup_scripts({
                "check_dependencies": True,
                "port_availability": True
            })
            
            if not validation_result.success:
                # Repair by recreating scripts with current configuration
                repair_result = self._manage_startup_scripts({
                    "script_type": "complete_system",
                    "port_management": repair_port_configs,
                    "venv_management": fix_venv_references,
                    "health_checks": True,
                    "auto_browser_open": True
                })
                
                if repair_result.success:
                    repairs_made.extend(repair_result.data.get("scripts_created", []))
                    repairs_made.extend(repair_result.data.get("scripts_updated", []))
                else:
                    issues_found.append("Failed to recreate startup scripts")
            
            return AgentResult(
                success=len(issues_found) == 0,
                message=f"Repair completed. {len(repairs_made)} scripts repaired.",
                data={
                    "repairs_made": repairs_made,
                    "issues_found": issues_found,
                    "validation_after_repair": self._validate_startup_scripts({"test_execution": False}).data
                }
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Failed to repair startup scripts: {str(e)}",
                data={"error": str(e), "repairs_made": repairs_made}
            )

    def _diagnose_connections(self, parameters: Dict[str, Any]) -> AgentResult:
        """Diagnose connection issues across system components"""
        component = parameters.get("component", "all")
        predictive_analysis = parameters.get("predictive_analysis", True)
        auto_heal = parameters.get("auto_heal", False)
        
        diagnosis = {
            "services_status": {},
            "connection_health": {},
            "issues_found": [],
            "recommendations": []
        }
        
        # Check each service
        for service_name, config in self.services.items():
            if component == "all" or component == service_name:
                status = self._check_service_status(service_name, config)
                diagnosis["services_status"][service_name] = status
                
                if not status["healthy"]:
                    diagnosis["issues_found"].extend(status["issues"])
        
        # Generate recommendations
        diagnosis["recommendations"] = self._generate_connection_recommendations(diagnosis)
        
        return AgentResult(
            success=len(diagnosis["issues_found"]) == 0,
            message=f"Connection diagnosis completed for {component}",
            data=diagnosis
        )

    def _check_service_status(self, service_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """Check the status of a specific service"""
        status = {
            "healthy": True,
            "issues": [],
            "port_available": False,
            "process_running": False,
            "response_time": None
        }
        
        # Check port availability
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(2)
                result = sock.connect_ex(('localhost', config["port"]))
                status["port_available"] = result == 0
        except Exception as e:
            status["issues"].append(f"Port check failed: {str(e)}")
            status["healthy"] = False
        
        # Check if process is running (simplified)
        if status["port_available"]:
            process_info = self._get_process_on_port(config["port"])
            status["process_running"] = process_info is not None
        
        # Health endpoint check if available
        if "health_endpoint" in config:
            try:
                import requests
                response = requests.get(config["health_endpoint"], timeout=5)
                status["response_time"] = response.elapsed.total_seconds()
                if response.status_code != 200:
                    status["issues"].append(f"Health endpoint returned {response.status_code}")
                    status["healthy"] = False
            except Exception as e:
                status["issues"].append(f"Health endpoint check failed: {str(e)}")
                status["healthy"] = False
        
        return status

    def _integration_health_check(self, parameters: Dict[str, Any]) -> AgentResult:
        """Comprehensive integration health monitoring"""
        prediction_window = parameters.get("prediction_window", 48)
        alert_threshold = parameters.get("alert_threshold", "warning")
        
        health_data = {
            "overall_health": "healthy",
            "component_health": {},
            "predictions": {},
            "alerts": []
        }
        
        # Check all components
        for service_name, config in self.services.items():
            health = self._check_service_status(service_name, config)
            health_data["component_health"][service_name] = health
            
            if not health["healthy"]:
                health_data["overall_health"] = "degraded"
        
        return AgentResult(
            success=health_data["overall_health"] != "critical",
            message=f"Integration health check completed - Status: {health_data['overall_health']}",
            data=health_data
        )

    def _optimize_performance(self, parameters: Dict[str, Any]) -> AgentResult:
        """Optimize connection performance and reliability"""
        focus_area = parameters.get("focus_area", "all")
        
        optimizations = {
            "applied": [],
            "recommendations": [],
            "performance_metrics": {}
        }
        
        # Performance optimization recommendations
        optimizations["recommendations"] = [
            "Configure connection pooling for database connections",
            "Implement request timeout configurations",
            "Add circuit breaker patterns for external services",
            "Configure load balancing for high availability",
            "Implement caching strategies for frequently accessed data"
        ]
        
        return AgentResult(
            success=True,
            message=f"Performance optimization analysis completed for {focus_area}",
            data=optimizations
        )

    def _generate_startup_recommendations(self) -> List[str]:
        """Generate recommendations for startup script management"""
        return [
            "Schedule regular validation of startup scripts",
            "Implement automated health checks after service startup",
            "Consider using environment variables for configuration paths",
            "Add logging to startup scripts for better debugging",
            "Create fallback scripts for emergency recovery"
        ]

    def _generate_validation_recommendations(self, validation_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        if validation_results["issues_found"]:
            recommendations.append("Run script repair to fix identified issues")
            
        if len(validation_results["validated_scripts"]) < 4:
            recommendations.append("Create missing startup scripts for complete coverage")
            
        recommendations.extend([
            "Test startup scripts in clean environment before deployment",
            "Document startup procedures for team members",
            "Implement monitoring for service startup times"
        ])
        
        return recommendations

    def _generate_connection_recommendations(self, diagnosis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on connection diagnosis"""
        recommendations = []
        
        if diagnosis["issues_found"]:
            recommendations.append("Address identified connection issues immediately")
            
        recommendations.extend([
            "Implement health check endpoints for all services",
            "Configure automatic service restart on failure",
            "Add connection retry logic with exponential backoff",
            "Monitor service dependencies and startup order"
        ])
        
        return recommendations

    def _cleanup_obsolete_scripts(self, parameters: Dict[str, Any]) -> AgentResult:
        """Detect and remove obsolete startup/shutdown scripts and batch files"""
        dry_run = parameters.get("dry_run", True)
        backup_before_delete = parameters.get("backup_before_delete", True)
        cleanup_scope = parameters.get("cleanup_scope", "startup_shutdown")
        preserve_patterns = parameters.get("preserve_patterns", [
            "START_COMPLETE_SYSTEM_PORT_3003.bat",
            "START_*_ONLY.bat",
            "STOP_*.bat"
        ])
        
        results = {
            "obsolete_files_found": [],
            "files_preserved": [],
            "files_deleted": [],
            "backup_created": None,
            "dry_run": dry_run,
            "cleanup_scope": cleanup_scope
        }
        
        try:
            # Define patterns for different cleanup scopes
            if cleanup_scope == "startup_shutdown":
                search_patterns = [
                    "START*.bat",
                    "STOP*.bat", 
                    "start*.bat",
                    "stop*.bat",
                    "run*.bat",
                    "launch*.bat",
                    "startup*.bat",
                    "shutdown*.bat"
                ]
            elif cleanup_scope == "all_batch":
                search_patterns = ["*.bat", "*.cmd"]
            else:  # custom
                search_patterns = parameters.get("custom_patterns", ["*.bat"])
            
            # Find all potentially obsolete files
            obsolete_candidates = []
            for pattern in search_patterns:
                for file_path in self.project_root.glob(pattern):
                    if file_path.is_file():
                        obsolete_candidates.append(file_path)
            
            # Check each file against preserve patterns
            for file_path in obsolete_candidates:
                file_name = file_path.name
                should_preserve = False
                
                # Check if file matches any preserve pattern
                for preserve_pattern in preserve_patterns:
                    import fnmatch
                    if fnmatch.fnmatch(file_name, preserve_pattern):
                        should_preserve = True
                        results["files_preserved"].append(str(file_path))
                        break
                
                # Check if file is currently active/functional
                if not should_preserve:
                    is_obsolete = self._is_file_obsolete(file_path)
                    if is_obsolete:
                        results["obsolete_files_found"].append({
                            "path": str(file_path),
                            "size": file_path.stat().st_size,
                            "modified": file_path.stat().st_mtime,
                            "reason": is_obsolete
                        })
            
            # Create backup if needed and files found
            if backup_before_delete and results["obsolete_files_found"] and not dry_run:
                backup_path = self._create_obsolete_files_backup(results["obsolete_files_found"])
                results["backup_created"] = str(backup_path)
            
            # Delete obsolete files (if not dry run)
            if not dry_run and results["obsolete_files_found"]:
                for file_info in results["obsolete_files_found"]:
                    file_path = Path(file_info["path"])
                    try:
                        if file_path.exists():
                            file_path.unlink()
                            results["files_deleted"].append(file_info["path"])
                    except Exception as e:
                        file_info["deletion_error"] = str(e)
            
            # Generate summary message
            found_count = len(results["obsolete_files_found"])
            deleted_count = len(results["files_deleted"])
            preserved_count = len(results["files_preserved"])
            
            if dry_run:
                message = f"Dry run: Found {found_count} obsolete files, {preserved_count} preserved files"
            else:
                message = f"Cleanup complete: Deleted {deleted_count}/{found_count} obsolete files, {preserved_count} preserved"
            
            return AgentResult(
                success=True,
                message=message,
                data=results
            )
            
        except Exception as e:
            return AgentResult(
                success=False,
                message=f"Failed to cleanup obsolete scripts: {str(e)}",
                data={"error": str(e), "partial_results": results}
            )

    def _is_file_obsolete(self, file_path: Path) -> Optional[str]:
        """Determine if a file is obsolete and return the reason"""
        file_name = file_path.name.lower()
        file_content = ""
        
        try:
            # Read file content for analysis
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                file_content = f.read()
        except:
            return "Unable to read file content"
        
        # Check for various obsolete indicators
        if file_path.stat().st_size == 0:
            return "Empty file (0 bytes)"
        
        # Check for obsolete paths or references
        obsolete_indicators = [
            "Comfyvenv\\Scripts\\activate",  # Old venv path
            "START_ALL.bat",  # Duplicated by newer scripts
            "start_all_services.bat",  # Replaced by component scripts
            "run_nvidia_gpu.bat",  # ComfyUI specific, should be in ComfyUI dir
            "test_backend_env.bat",  # Development test scripts
            "test_backend_fix.bat",
            "test_comfyui_xformers.bat"
        ]
        
        for indicator in obsolete_indicators:
            if indicator.lower() in file_content.lower():
                return f"Contains obsolete reference: {indicator}"
        
        # Check for backup files (with timestamps)
        if ".backup_" in file_name:
            return "Backup file"
        
        # Check for duplicate functionality
        if "start_all" in file_name and file_name != "start_all_services.bat":
            return "Duplicate startup functionality"
        
        # Check for empty or minimal batch files
        lines = [line.strip() for line in file_content.split('\n') if line.strip()]
        non_comment_lines = [line for line in lines if not line.startswith('REM') and not line.startswith('echo')]
        
        if len(non_comment_lines) < 3:
            return "Minimal functionality (less than 3 commands)"
        
        return None  # Not obsolete

    def _create_obsolete_files_backup(self, obsolete_files: List[Dict]) -> Path:
        """Create a backup archive of obsolete files before deletion"""
        import zipfile
        from datetime import datetime
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_dir = self.project_root / "backups"
        backup_dir.mkdir(exist_ok=True)
        
        backup_path = backup_dir / f"obsolete_scripts_backup_{timestamp}.zip"
        
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_info in obsolete_files:
                file_path = Path(file_info["path"])
                if file_path.exists():
                    # Add file to zip with relative path
                    arcname = file_path.relative_to(self.project_root)
                    zipf.write(file_path, arcname)
        
        return backup_path

async def execute(context: Dict[str, Any]) -> AgentResult:
    """Main execution function called by the orchestrator"""
    manager = SystemConnectionsManager()
    manager.start_timing()
    
    try:
        task_name = context.get("task_name", "manage_startup_scripts")
        parameters = context.get("parameters", {})
        
        result = manager.execute_task(task_name, parameters)
        result.execution_time = manager.stop_timing()
        
        return result
        
    except Exception as e:
        return AgentResult(
            success=False,
            message=f"Execution error: {str(e)}",
            data={"error": str(e)},
            execution_time=manager.stop_timing()
        )

if __name__ == "__main__":
    manager = SystemConnectionsManager()
    
    # Test startup script management
    result = manager.execute_task("manage_startup_scripts", {
        "script_type": "complete_system",
        "port_management": True,
        "venv_management": True,
        "health_checks": True,
        "auto_browser_open": True
    })
    
    print(f"Result: {result.success}")
    print(f"Message: {result.message}")
    if result.data:
        print(f"Scripts created: {result.data.get('scripts_created', [])}")
        print(f"Scripts updated: {result.data.get('scripts_updated', [])}")
