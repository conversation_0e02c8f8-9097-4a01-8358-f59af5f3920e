#!/usr/bin/env python3
"""
Image Expert Agent
Advanced image processing and analysis specialist

This agent handles:
1. Image quality assessment and optimization
2. Image format conversion and processing
3. Computer vision analysis and object detection
4. Image generation parameter optimization
5. Visual content analysis and recommendations
"""

import asyncio
import json
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import base64
from PIL import Image, ImageStat
import io
import hashlib

# Import base agent functionality
sys.path.insert(0, str(Path(__file__).parent))
from base_agent import BaseAgent

class ImageExpertAgent(BaseAgent):
    """
    Image Expert Agent implementation.
    
    Provides comprehensive image analysis, processing, and optimization capabilities.
    Specializes in visual content assessment and enhancement recommendations.
    """
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.project_root = Path(context['config']['project_root'])
        
        # Image processing paths
        self.images_path = self.project_root / "images"
        self.output_path = self.project_root / "output"
        self.temp_path = self.project_root / "temp"
        
        # Supported image formats
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff', '.gif']
        
        # Quality thresholds
        self.quality_thresholds = {
            "excellent": 0.9,
            "good": 0.75,
            "fair": 0.6,
            "poor": 0.4
        }
    
    async def execute_task(self) -> Dict[str, Any]:
        """Execute the specific image expert task."""
        task_name = self.task_name
        
        if task_name == "analyze_images":
            return await self.analyze_images()
        elif task_name == "optimize_quality":
            return await self.optimize_quality()
        elif task_name == "convert_formats":
            return await self.convert_formats()
        elif task_name == "assess_generation_quality":
            return await self.assess_generation_quality()
        elif task_name == "extract_metadata":
            return await self.extract_metadata()
        elif task_name == "batch_process":
            return await self.batch_process()
        else:
            return await self.default_image_analysis()
    
    async def analyze_images(self) -> Dict[str, Any]:
        """Analyze images in the project for quality and characteristics."""
        self.logger.info("🖼️ Analyzing images...")
        
        analysis_results = {
            "total_images": 0,
            "format_distribution": {},
            "size_analysis": {
                "average_width": 0,
                "average_height": 0,
                "size_categories": {}
            },
            "quality_assessment": {
                "excellent": 0,
                "good": 0,
                "fair": 0,
                "poor": 0
            },
            "optimization_opportunities": [],
            "technical_issues": [],
            "recommendations": []
        }
        
        # Find all image files
        image_files = await self._find_image_files()
        analysis_results["total_images"] = len(image_files)
        
        if not image_files:
            return {
                "success": True,
                "analysis": analysis_results,
                "message": "No images found for analysis",
                "timestamp": datetime.now().isoformat()
            }
        
        # Analyze each image
        widths, heights = [], []
        
        for image_file in image_files:
            try:
                image_analysis = await self._analyze_single_image(image_file)
                
                # Update format distribution
                format_key = image_analysis["format"]
                if format_key in analysis_results["format_distribution"]:
                    analysis_results["format_distribution"][format_key] += 1
                else:
                    analysis_results["format_distribution"][format_key] = 1
                
                # Update size analysis
                widths.append(image_analysis["width"])
                heights.append(image_analysis["height"])
                
                # Update quality assessment
                quality_level = image_analysis["quality_level"]
                analysis_results["quality_assessment"][quality_level] += 1
                
                # Check for optimization opportunities
                if image_analysis.get("optimization_needed"):
                    analysis_results["optimization_opportunities"].append({
                        "file": str(image_file.relative_to(self.project_root)),
                        "issues": image_analysis.get("issues", []),
                        "potential_savings": image_analysis.get("potential_savings", "unknown")
                    })
                
                # Check for technical issues
                if image_analysis.get("technical_issues"):
                    analysis_results["technical_issues"].extend(image_analysis["technical_issues"])
                
            except Exception as e:
                self.logger.warning(f"Error analyzing image {image_file}: {e}")
                analysis_results["technical_issues"].append({
                    "file": str(image_file.relative_to(self.project_root)),
                    "error": str(e)
                })
        
        # Calculate size statistics
        if widths and heights:
            analysis_results["size_analysis"]["average_width"] = sum(widths) // len(widths)
            analysis_results["size_analysis"]["average_height"] = sum(heights) // len(heights)
            
            # Categorize sizes
            for width, height in zip(widths, heights):
                category = self._categorize_image_size(width, height)
                if category in analysis_results["size_analysis"]["size_categories"]:
                    analysis_results["size_analysis"]["size_categories"][category] += 1
                else:
                    analysis_results["size_analysis"]["size_categories"][category] = 1
        
        # Generate recommendations
        recommendations = await self._generate_image_recommendations(analysis_results)
        analysis_results["recommendations"] = recommendations
        
        return {
            "success": True,
            "analysis": analysis_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def _find_image_files(self) -> List[Path]:
        """Find all image files in the project."""
        image_files = []
        
        # Search in common image directories
        search_paths = [
            self.images_path,
            self.output_path,
            self.project_root / "data",
            self.project_root / "assets",
            self.project_root / "static",
            self.project_root / "public"
        ]
        
        for search_path in search_paths:
            if search_path.exists():
                for format_ext in self.supported_formats:
                    pattern = f"**/*{format_ext}"
                    found_files = list(search_path.glob(pattern))
                    image_files.extend(found_files)
                    
                    # Also search for uppercase extensions
                    pattern_upper = f"**/*{format_ext.upper()}"
                    found_files_upper = list(search_path.glob(pattern_upper))
                    image_files.extend(found_files_upper)
        
        # Remove duplicates
        image_files = list(set(image_files))
        
        return image_files
    
    async def _analyze_single_image(self, image_path: Path) -> Dict[str, Any]:
        """Analyze a single image file."""
        image_analysis = {
            "path": str(image_path.relative_to(self.project_root)),
            "format": "unknown",
            "width": 0,
            "height": 0,
            "file_size": 0,
            "quality_score": 0.0,
            "quality_level": "unknown",
            "issues": [],
            "optimization_needed": False,
            "technical_issues": []
        }
        
        try:
            # Get file size
            image_analysis["file_size"] = image_path.stat().st_size
            
            # Open and analyze image
            with Image.open(image_path) as img:
                image_analysis["width"] = img.width
                image_analysis["height"] = img.height
                image_analysis["format"] = img.format.lower() if img.format else "unknown"
                
                # Calculate quality score
                quality_score = await self._calculate_quality_score(img, image_path)
                image_analysis["quality_score"] = quality_score
                image_analysis["quality_level"] = self._get_quality_level(quality_score)
                
                # Check for common issues
                issues = await self._check_image_issues(img, image_path)
                image_analysis["issues"] = issues
                image_analysis["optimization_needed"] = len(issues) > 0
                
                # Calculate potential savings
                if image_analysis["optimization_needed"]:
                    savings = await self._calculate_potential_savings(img, image_path)
                    image_analysis["potential_savings"] = savings
        
        except Exception as e:
            image_analysis["technical_issues"].append({
                "type": "analysis_error",
                "message": str(e)
            })
        
        return image_analysis
    
    async def _calculate_quality_score(self, img: Image.Image, image_path: Path) -> float:
        """Calculate an overall quality score for the image."""
        quality_factors = []
        
        try:
            # Factor 1: Resolution appropriateness
            total_pixels = img.width * img.height
            if total_pixels >= 1920 * 1080:  # Full HD or higher
                resolution_score = 1.0
            elif total_pixels >= 1280 * 720:  # HD
                resolution_score = 0.8
            elif total_pixels >= 640 * 480:   # VGA
                resolution_score = 0.6
            else:
                resolution_score = 0.4
            quality_factors.append(resolution_score)
            
            # Factor 2: File size efficiency
            file_size = image_path.stat().st_size
            pixels = img.width * img.height
            bytes_per_pixel = file_size / pixels if pixels > 0 else 0
            
            # Optimal bytes per pixel varies by format
            if img.format == 'JPEG':
                optimal_bpp = 3  # Good quality JPEG
                efficiency_score = min(1.0, optimal_bpp / bytes_per_pixel) if bytes_per_pixel > 0 else 0.5
            elif img.format == 'PNG':
                optimal_bpp = 6  # Good quality PNG
                efficiency_score = min(1.0, optimal_bpp / bytes_per_pixel) if bytes_per_pixel > 0 else 0.5
            else:
                efficiency_score = 0.7  # Default for other formats
            
            quality_factors.append(efficiency_score)
            
            # Factor 3: Aspect ratio reasonableness
            aspect_ratio = img.width / img.height if img.height > 0 else 1
            if 0.5 <= aspect_ratio <= 3.0:  # Reasonable aspect ratio
                aspect_score = 1.0
            elif 0.3 <= aspect_ratio <= 5.0:  # Acceptable
                aspect_score = 0.8
            else:
                aspect_score = 0.5
            quality_factors.append(aspect_score)
            
            # Factor 4: Color analysis (if possible)
            if img.mode in ['RGB', 'RGBA']:
                stat = ImageStat.Stat(img)
                # Check for color variety (simple heuristic)
                color_variance = sum(stat.var) / len(stat.var) if stat.var else 0
                if color_variance > 1000:  # Good color variety
                    color_score = 1.0
                elif color_variance > 500:
                    color_score = 0.8
                elif color_variance > 100:
                    color_score = 0.6
                else:
                    color_score = 0.4
                quality_factors.append(color_score)
            
        except Exception as e:
            self.logger.debug(f"Error calculating quality score: {e}")
            return 0.5  # Default score if analysis fails
        
        # Calculate weighted average
        if quality_factors:
            return sum(quality_factors) / len(quality_factors)
        else:
            return 0.5
    
    def _get_quality_level(self, quality_score: float) -> str:
        """Convert quality score to quality level."""
        if quality_score >= self.quality_thresholds["excellent"]:
            return "excellent"
        elif quality_score >= self.quality_thresholds["good"]:
            return "good"
        elif quality_score >= self.quality_thresholds["fair"]:
            return "fair"
        else:
            return "poor"
    
    async def _check_image_issues(self, img: Image.Image, image_path: Path) -> List[str]:
        """Check for common image issues."""
        issues = []
        
        # Check file size
        file_size = image_path.stat().st_size
        if file_size > 10 * 1024 * 1024:  # 10MB
            issues.append("Large file size (>10MB)")
        elif file_size > 5 * 1024 * 1024:  # 5MB
            issues.append("Moderately large file size (>5MB)")
        
        # Check dimensions
        if img.width > 4000 or img.height > 4000:
            issues.append("Very high resolution (>4000px)")
        elif img.width < 100 or img.height < 100:
            issues.append("Very low resolution (<100px)")
        
        # Check aspect ratio
        aspect_ratio = img.width / img.height if img.height > 0 else 1
        if aspect_ratio > 5 or aspect_ratio < 0.2:
            issues.append("Unusual aspect ratio")
        
        # Check format efficiency
        if img.format == 'BMP':
            issues.append("Inefficient format (BMP)")
        elif img.format == 'TIFF' and file_size > 1024 * 1024:
            issues.append("Large TIFF file")
        
        return issues
    
    async def _calculate_potential_savings(self, img: Image.Image, image_path: Path) -> str:
        """Calculate potential file size savings."""
        current_size = image_path.stat().st_size
        
        # Estimate optimized size (very rough approximation)
        if img.format == 'PNG' and img.mode == 'RGB':
            # PNG could be converted to JPEG
            estimated_size = current_size * 0.3  # Rough estimate
            savings_percent = (1 - estimated_size / current_size) * 100
            return f"~{savings_percent:.0f}% (PNG→JPEG conversion)"
        elif img.format == 'BMP':
            estimated_size = current_size * 0.1
            savings_percent = (1 - estimated_size / current_size) * 100
            return f"~{savings_percent:.0f}% (BMP→PNG/JPEG conversion)"
        elif current_size > 5 * 1024 * 1024:  # Large files
            return "~20-50% (compression optimization)"
        else:
            return "~10-20% (minor optimizations)"
    
    def _categorize_image_size(self, width: int, height: int) -> str:
        """Categorize image by size."""
        total_pixels = width * height
        
        if total_pixels >= 3840 * 2160:  # 4K
            return "4K+"
        elif total_pixels >= 1920 * 1080:  # Full HD
            return "Full HD"
        elif total_pixels >= 1280 * 720:   # HD
            return "HD"
        elif total_pixels >= 640 * 480:    # VGA
            return "Standard"
        else:
            return "Small"
    
    async def _generate_image_recommendations(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate image optimization recommendations."""
        recommendations = []
        
        # Check for format optimization opportunities
        format_dist = analysis.get("format_distribution", {})
        if "bmp" in format_dist:
            recommendations.append({
                "type": "format_optimization",
                "priority": "high",
                "title": "Convert BMP files to modern formats",
                "description": f"Found {format_dist['bmp']} BMP files that could be optimized",
                "implementation": "Convert BMP files to PNG or JPEG for better compression"
            })
        
        # Check quality distribution
        quality_assessment = analysis.get("quality_assessment", {})
        poor_quality = quality_assessment.get("poor", 0)
        total_images = analysis.get("total_images", 0)
        
        if poor_quality > 0 and total_images > 0:
            poor_percentage = (poor_quality / total_images) * 100
            if poor_percentage > 25:
                recommendations.append({
                    "type": "quality_improvement",
                    "priority": "high",
                    "title": "Many low quality images detected",
                    "description": f"{poor_percentage:.1f}% of images have poor quality scores",
                    "implementation": "Review and replace low quality images"
                })
        
        # Check optimization opportunities
        optimization_opps = analysis.get("optimization_opportunities", [])
        if len(optimization_opps) > 0:
            recommendations.append({
                "type": "file_optimization",
                "priority": "medium",
                "title": "File size optimization opportunities",
                "description": f"Found {len(optimization_opps)} images that can be optimized",
                "implementation": "Run batch optimization on identified images"
            })
        
        # Check size distribution
        size_categories = analysis.get("size_analysis", {}).get("size_categories", {})
        large_images = size_categories.get("4K+", 0)
        if large_images > 5:
            recommendations.append({
                "type": "performance",
                "priority": "medium",
                "title": "Many 4K+ images detected",
                "description": f"Found {large_images} very high resolution images",
                "implementation": "Consider resizing for web use or creating thumbnails"
            })
        
        return recommendations
    
    async def optimize_quality(self) -> Dict[str, Any]:
        """Optimize image quality and file sizes."""
        self.logger.info("⚡ Optimizing image quality...")
        
        target_path = self.parameters.get("target_path", self.images_path)
        if isinstance(target_path, str):
            target_path = Path(target_path)
        
        optimization_results = {
            "processed_images": 0,
            "total_size_before": 0,
            "total_size_after": 0,
            "optimizations_applied": [],
            "errors": []
        }
        
        # This would implement actual image optimization
        # For now, return structure
        return {
            "success": True,
            "optimization": optimization_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def convert_formats(self) -> Dict[str, Any]:
        """Convert images between formats."""
        self.logger.info("🔄 Converting image formats...")
        
        source_format = self.parameters.get("source_format", "bmp")
        target_format = self.parameters.get("target_format", "png")
        
        conversion_results = {
            "converted_files": 0,
            "conversion_details": [],
            "space_saved": 0,
            "errors": []
        }
        
        # This would implement actual format conversion
        return {
            "success": True,
            "conversion": conversion_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def assess_generation_quality(self) -> Dict[str, Any]:
        """Assess quality of generated images."""
        self.logger.info("🎯 Assessing image generation quality...")
        
        assessment_results = {
            "generation_metrics": {},
            "quality_scores": [],
            "improvement_suggestions": [],
            "parameter_recommendations": []
        }
        
        # This would implement generation quality assessment
        return {
            "success": True,
            "assessment": assessment_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def extract_metadata(self) -> Dict[str, Any]:
        """Extract metadata from images."""
        self.logger.info("📋 Extracting image metadata...")
        
        metadata_results = {
            "total_processed": 0,
            "metadata_summary": {},
            "generation_parameters": [],
            "camera_info": [],
            "technical_details": []
        }
        
        # This would implement metadata extraction
        return {
            "success": True,
            "metadata": metadata_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def batch_process(self) -> Dict[str, Any]:
        """Batch process multiple images."""
        self.logger.info("🔄 Batch processing images...")
        
        operation = self.parameters.get("operation", "analyze")
        
        batch_results = {
            "operation": operation,
            "processed_count": 0,
            "results": [],
            "errors": []
        }
        
        # This would implement actual batch processing
        return {
            "success": True,
            "batch": batch_results,
            "timestamp": datetime.now().isoformat()
        }
    
    async def default_image_analysis(self) -> Dict[str, Any]:
        """Default comprehensive image analysis."""
        return await self.analyze_images()

# Entry point for orchestration system
async def execute(context):
    """Execute function required by the orchestration system."""
    agent = ImageExpertAgent(context)
    return await agent.execute_task()

if __name__ == "__main__":
    # Test execution
    test_context = {
        "agent": {"name": "image-expert"},
        "task": {"name": "analyze_images", "parameters": {}},
        "config": {"project_root": str(Path(__file__).parent.parent.parent)},
        "knowledge_bases": {}
    }
    
    import asyncio
    result = asyncio.run(execute(test_context))
    print(json.dumps(result, indent=2))
