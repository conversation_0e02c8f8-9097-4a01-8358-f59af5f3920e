#!/usr/bin/env pwsh
# PowerShell Crash Diagnostic Script
# Comprehensive analysis of VS Code PowerShell integration issues

Write-Host "=== VS Code PowerShell Crash Diagnostic ===" -ForegroundColor Cyan
Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Gray
Write-Host ""

# Check PowerShell version and environment
Write-Host "1. PowerShell Environment:" -ForegroundColor Yellow
Write-Host "   Version: $($PSVersionTable.PSVersion)" -ForegroundColor Green
Write-Host "   Edition: $($PSVersionTable.PSEdition)" -ForegroundColor Green
Write-Host "   OS: $($PSVersionTable.OS)" -ForegroundColor Green
Write-Host ""

# Check profile locations and existence
Write-Host "2. Profile Analysis:" -ForegroundColor Yellow
$profileLocations = @{
    "AllUsersAllHosts" = $PROFILE.AllUsersAllHosts
    "AllUsersCurrentHost" = $PROFILE.AllUsersCurrentHost  
    "CurrentUserAllHosts" = $PROFILE.CurrentUserAllHosts
    "CurrentUserCurrentHost" = $PROFILE.CurrentUserCurrentHost
}

foreach ($profileName in $profileLocations.Keys) {
    $profilePath = $profileLocations[$profileName]
    if (Test-Path $profilePath) {
        Write-Host "   ✅ EXISTS: $profileName" -ForegroundColor Green
        Write-Host "      Path: $profilePath" -ForegroundColor Gray
        try {
            $content = Get-Content $profilePath -Raw
            Write-Host "      Size: $($content.Length) characters" -ForegroundColor Gray
            if ($content -match "prompt|PSReadLine|oh-my-posh|posh-git") {
                Write-Host "      ⚠️  Contains prompt/readline customizations" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "      ❌ Error reading profile: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "   ❌ MISSING: $profileName" -ForegroundColor Red
        Write-Host "      Path: $profilePath" -ForegroundColor Gray
    }
}
Write-Host ""

# Check modules that commonly cause issues
Write-Host "3. Module Analysis:" -ForegroundColor Yellow
$problematicModules = @("PSReadLine", "posh-git", "oh-my-posh", "PowerLine", "Terminal-Icons")

foreach ($module in $problematicModules) {
    try {
        $moduleInfo = Get-Module $module -ListAvailable | Select-Object -First 1
        if ($moduleInfo) {
            Write-Host "   📦 $module" -ForegroundColor Green
            Write-Host "      Version: $($moduleInfo.Version)" -ForegroundColor Gray
            Write-Host "      Path: $($moduleInfo.ModuleBase)" -ForegroundColor Gray
            
            # Check if module is currently loaded
            if (Get-Module $module) {
                Write-Host "      ✅ Currently loaded" -ForegroundColor Green
            } else {
                Write-Host "      ⭕ Available but not loaded" -ForegroundColor Yellow
            }
        } else {
            Write-Host "   ❌ $module - Not installed" -ForegroundColor Red
        }
    } catch {
        Write-Host "   ❌ $module - Error checking: $($_.Exception.Message)" -ForegroundColor Red
    }
}
Write-Host ""

# Check environment variables
Write-Host "4. Environment Variables:" -ForegroundColor Yellow
$envVars = @("TERM_PROGRAM", "VSCODE_INJECTION", "VSCODE_PID", "PSMODULEPATH")
foreach ($envVar in $envVars) {
    $value = [Environment]::GetEnvironmentVariable($envVar)
    if ($value) {
        Write-Host "   ✅ $envVar = $value" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $envVar - Not set" -ForegroundColor Red
    }
}
Write-Host ""

# Check VS Code specific paths
Write-Host "5. VS Code Integration:" -ForegroundColor Yellow
try {
    $shellIntegrationPath = code --locate-shell-integration-path pwsh 2>$null
    if ($shellIntegrationPath) {
        Write-Host "   ✅ Shell integration path: $shellIntegrationPath" -ForegroundColor Green
        if (Test-Path $shellIntegrationPath) {
            Write-Host "   ✅ Shell integration script exists" -ForegroundColor Green
        } else {
            Write-Host "   ❌ Shell integration script missing" -ForegroundColor Red
        }
    } else {
        Write-Host "   ❌ Could not locate shell integration path" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Error checking VS Code integration: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test minimal prompt function
Write-Host "6. Prompt Function Test:" -ForegroundColor Yellow
try {
    $currentPrompt = Get-Command prompt -ErrorAction SilentlyContinue
    if ($currentPrompt) {
        Write-Host "   ✅ Prompt function exists" -ForegroundColor Green
        Write-Host "   Source: $($currentPrompt.Source)" -ForegroundColor Gray
        
        # Test if prompt function causes recursion
        $promptResult = & {
            try {
                $oldErrorActionPreference = $ErrorActionPreference
                $ErrorActionPreference = "Stop"
                $result = prompt
                $ErrorActionPreference = $oldErrorActionPreference
                return $result
            } catch {
                return "ERROR: $($_.Exception.Message)"
            }
        }
        Write-Host "   Prompt result: '$promptResult'" -ForegroundColor Gray
    } else {
        Write-Host "   ❌ No prompt function found" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Error testing prompt: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Memory and performance check
Write-Host "7. System Resources:" -ForegroundColor Yellow
try {
    $process = Get-Process -Name "pwsh" -ErrorAction SilentlyContinue | Where-Object { $_.Id -eq $PID }
    if ($process) {
        Write-Host "   ✅ Current PowerShell process:" -ForegroundColor Green
        Write-Host "      PID: $($process.Id)" -ForegroundColor Gray
        Write-Host "      Memory: $([math]::Round($process.WorkingSet64 / 1MB, 2)) MB" -ForegroundColor Gray
        Write-Host "      CPU Time: $($process.TotalProcessorTime)" -ForegroundColor Gray
    }
} catch {
    Write-Host "   ❌ Error getting process info: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Recommendations
Write-Host "8. Recommendations:" -ForegroundColor Yellow
Write-Host "   💡 Based on analysis above:" -ForegroundColor Cyan

# Check if VS Code settings disable profile loading
$vsCodeSettingsPath = ".vscode/settings.json"
if (Test-Path $vsCodeSettingsPath) {
    try {
        $settings = Get-Content $vsCodeSettingsPath -Raw | ConvertFrom-Json
        if ($settings."powershell.enableProfileLoading" -eq $false) {
            Write-Host "   ✅ Profile loading disabled in VS Code (good for preventing crashes)" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️  Consider adding 'powershell.enableProfileLoading': false to VS Code settings" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   ❌ Error reading VS Code settings: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "   ⚠️  No VS Code settings.json found in current directory" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Diagnostic Complete ===" -ForegroundColor Cyan
Write-Host "Save this output and share with support if issues persist." -ForegroundColor Gray
