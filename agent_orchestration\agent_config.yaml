# ComfyUI Frontend Agent Orchestration Configuration
# Central configuration for all specialized agents and their capabilities

version: "2.0.0"
project: "ComfyUI Frontend"
last_updated: "2025-01-07"

# Global configuration settings
global:
  project_root: "G:\\comfyui_Front"
  knowledge_base_dir: "G:\\comfyui_Front"
  data_dir: "G:\\comfyui_Front\\data\\agents"
  logs_dir: "G:\\comfyui_Front\\logs\\agents"
  reports_dir: "G:\\comfyui_Front\\reports\\agents"
  
  # Notification settings (optional)
  notifications:
    slack_webhook: null  # Set to webhook URL if desired
    discord_webhook: null  # Set to webhook URL if desired
    email_notifications: false
    
  # Default timeouts and retries
  default_timeout: 300  # 5 minutes
  max_retries: 3
  retry_delay: 5  # seconds

# Agent definitions with their capabilities and configurations
agents:
  
  # Infrastructure & Operations Agents
  system-optimization-agent:
    description: "Elite AI image generation optimization specialist focused on maximizing ComfyUI workflow performance, GPU efficiency, and creating the optimal environment for AI models to run at peak speed and quality"
    category: "infrastructure"
    color: "yellow"
    entrypoint: "scripts/systemoptimizer_wrapper.py"
    knowledge_bases:
      - "SYSTEM_OPTIMIZATION_SOLUTIONS.md"
      - "Frontend_Backend_Solutions.md"
      - "Dependency_Resolution_Solutions.md"
      - "RTX_4070_TI_SUPER_OPTIMIZATIONS.md"
      - "COMFYUI_WORKFLOW_KNOWLEDGE_BASE.md"
      - "model_inventory.md"
    databases:
      - "systemoptimization.db"
      - "performancebenchmarks.db"
      - "hardwareprofiles.db"
      - "ai_model_performance.db"
      - "generation_speed_metrics.db"
    
    tasks:
      systemaudit:
        description: "Comprehensive AI image generation performance audit - analyze GPU utilization, VRAM efficiency, model loading times, and identify bottlenecks specific to ComfyUI workflows"
        parameters:
          includeharware:
            type: boolean
            default: true
            description: "Include AI-focused hardware performance analysis (GPU, VRAM, CUDA cores)"
          depth:
            type: string
            default: "comprehensive"
            options: ["basic", "standard", "comprehensive", "expert"]
            description: "Depth of AI optimization analysis"
          focus_models:
            type: array
            default: ["flux", "sdxl", "hidream"]
            description: "Specific AI models to optimize for"
          benchmark_generation:
            type: boolean
            default: true
            description: "Run actual image generation benchmarks"
            
      optimizeperformance:
        description: "Apply AI-focused system optimizations for maximum image generation speed and quality"
        parameters:
          focusarea:
            type: string
            default: "ai_generation"
            options: ["gpu", "vram", "model_loading", "workflow_execution", "ai_generation", "all"]
            description: "Primary AI optimization focus"
          generation_speed_target:
            type: string
            default: "maximum"
            options: ["balanced", "maximum", "quality_focused"]
            description: "Speed vs quality optimization target"
          model_optimization:
            type: boolean
            default: true
            description: "Optimize model loading and inference speed"
          vram_optimization:
            type: boolean
            default: true
            description: "Optimize VRAM usage for larger models and batch sizes"
            
      hardwareoptimization:
        description: "RTX 4070 Ti SUPER AI workload optimization - maximize Tensor Core utilization and AI model performance"
        parameters:
          targetworkload:
            type: string
            default: "comfyui"
            options: ["comfyui", "flux", "sdxl", "hidream", "general_ai"]
            description: "Target AI workload type for optimization"
          tensor_core_optimization:
            type: boolean
            default: true
            description: "Optimize Tensor Core utilization for AI models"
          memory_efficiency:
            type: string
            default: "aggressive"
            options: ["conservative", "balanced", "aggressive"]
            description: "VRAM and system memory optimization level"
          cuda_optimization:
            type: boolean
            default: true
            description: "Optimize CUDA settings for AI inference"
            
      ai_environment_setup:
        description: "Configure optimal environment for AI model execution - drivers, CUDA, memory allocation"
        parameters:
          driver_optimization:
            type: boolean
            default: true
            description: "Optimize GPU drivers for AI workloads"
          cuda_environment:
            type: boolean
            default: true
            description: "Configure optimal CUDA environment"
          model_cache_optimization:
            type: boolean
            default: true
            description: "Optimize model caching and loading strategies"
          batch_size_optimization:
            type: boolean
            default: true
            description: "Determine optimal batch sizes for different models"
            
      generation_speed_analysis:
        description: "Analyze and optimize image generation pipeline speed from prompt to final image"
        parameters:
          workflow_profiling:
            type: boolean
            default: true
            description: "Profile ComfyUI workflow execution times"
          bottleneck_identification:
            type: boolean
            default: true
            description: "Identify specific bottlenecks in generation pipeline"
          model_comparison:
            type: boolean
            default: true
            description: "Compare performance across different models"
          optimization_recommendations:
            type: boolean
            default: true
            description: "Generate specific recommendations for speed improvements"
            
      thermaloptimization:
        description: "Thermal and power management optimization"
        parameters:
          temperaturetarget:
            type: integer
            default: 75
            description: "Target maximum GPU temperature in Celsius"
          powerefficiency:
            type: boolean
            default: false
            description: "Prioritize power efficiency over performance"
            
      cleanupoptimization:
        description: "System cleanup and maintenance optimization"
        parameters:
          deepclean:
            type: boolean
            default: false
            description: "Perform deep system cleanup including registry"
          autostart:
            type: boolean
            default: true
            description: "Optimize startup programs and services"

  dependency-orchestrator:
    description: "Elite dependency resolution specialist for complex multi-environment projects - ensures optimal AI model dependencies, Python packages, and development environment configuration for peak ComfyUI performance"
    category: "infrastructure"
    color: "green"
    entrypoint: "scripts/dependency_orchestrator.py"
    knowledge_bases:
      - "Dependency_Resolution_Solutions.md"
      - "SETUP_CONFIRMED.md" 
      - "README.md"
      - "model_inventory.md"
      - "requirements.txt"
      - "package.json"
    databases:
      - "dependency_tracking.db"
      - "ai_model_dependencies.db"
      - "performance_impact_analysis.db"
    
    tasks:
      resolve_conflicts:
        description: "Analyze and resolve dependency conflicts with focus on AI model compatibility and performance optimization"
        parameters:
          strict:
            type: boolean
            default: false
            description: "Use strict version resolution"
          report_mode:
            type: string
            default: "summary"
            options: ["summary", "full", "detailed"]
            description: "Level of detail in reports"
          target_environment:
            type: string
            default: "all"
            options: ["frontend", "backend", "comfyui", "ai_models", "all"]
            description: "Which environment to focus on"
          auto_fix:
            type: boolean
            default: true
            description: "Automatically apply fixes when possible"
          ai_model_focus:
            type: boolean
            default: true
            description: "Prioritize AI model compatibility in resolution"
          performance_impact:
            type: boolean
            default: true
            description: "Consider performance impact of dependency choices"
            
      environment_audit:
        description: "Comprehensive audit of all project environments with AI optimization recommendations"
        parameters:
          include_security:
            type: boolean
            default: true
            description: "Include security vulnerability scanning"
          performance_analysis:
            type: boolean
            default: true
            description: "Include AI performance impact analysis"
          model_compatibility:
            type: boolean
            default: true
            description: "Check AI model compatibility across environments"
          optimization_recommendations:
            type: boolean
            default: true
            description: "Generate environment optimization recommendations"
            
      setup_validation:
        description: "Validate complete environment setup for new developers"
        parameters:
          create_backup:
            type: boolean
            default: true
            description: "Create backup before making changes"
          test_connections:
            type: boolean
            default: true
            description: "Test all service connections"

  system-connections-manager:
    description: "Elite infrastructure architect with predictive integration health monitoring AND startup script management - maintains functional system startup scripts, handles ports, virtual environments, and ensures reliable system initialization"
    category: "infrastructure"
    color: "red"
    entrypoint: "scripts/connections_manager.py"
    knowledge_bases:
      - "Frontend_Backend_Solutions.md"
      - "README_DEVELOPMENT_REFERENCE.md"
      - "Dependency_Resolution_Solutions.md"
      - "BATCH_FILES_GUIDE.md"
    databases:
      - "integration_health.db"
      - "connection_metrics.db"
      - "startup_scripts.db"
    
    tasks:
      diagnose_connections:
        description: "Diagnose and fix connection issues across system components"
        parameters:
          component:
            type: string
            default: "all"
            options: ["frontend", "backend", "comfyui", "ollama", "websocket", "all"]
            description: "Which component to focus on"
          predictive_analysis:
            type: boolean
            default: true
            description: "Include predictive failure analysis"
          auto_heal:
            type: boolean
            default: false
            description: "Automatically apply healing actions"
            
      integration_health_check:
        description: "Comprehensive integration health monitoring and prediction"
        parameters:
          prediction_window:
            type: integer
            default: 48
            description: "Hours ahead to predict potential issues"
          alert_threshold:
            type: string
            default: "warning"
            options: ["advisory", "warning", "critical"]
            description: "Minimum alert level to report"
            
      optimize_performance:
        description: "Optimize connection performance and reliability"
        parameters:
          focus_area:
            type: string
            default: "all"
            options: ["latency", "throughput", "reliability", "security", "all"]
            description: "Primary optimization focus"
            
      manage_startup_scripts:
        description: "Create and maintain functional startup scripts with proper port management and virtual environment configuration"
        parameters:
          script_type:
            type: string
            default: "complete_system"
            options: ["complete_system", "backend_only", "frontend_only", "comfyui_only", "custom"]
            description: "Type of startup script to manage"
          port_management:
            type: boolean
            default: true
            description: "Include automatic port conflict detection and resolution"
          venv_management:
            type: boolean
            default: true
            description: "Include virtual environment activation and validation"
          health_checks:
            type: boolean
            default: true
            description: "Include service health verification"
          auto_browser_open:
            type: boolean
            default: true
            description: "Automatically open browser after successful startup"
            
      validate_startup_scripts:
        description: "Validate all startup scripts for correctness and functionality"
        parameters:
          test_execution:
            type: boolean
            default: false
            description: "Actually test script execution (dry run if false)"
          check_dependencies:
            type: boolean
            default: true
            description: "Verify all dependencies and paths exist"
          port_availability:
            type: boolean
            default: true
            description: "Check for port conflicts before validation"
            
      repair_startup_scripts:
        description: "Automatically repair broken or outdated startup scripts"
        parameters:
          backup_existing:
            type: boolean
            default: true
            description: "Create backup of existing scripts before repair"
          update_paths:
            type: boolean
            default: true
            description: "Update file paths to current system configuration"
          fix_venv_references:
            type: boolean
            default: true
            description: "Fix virtual environment activation commands"
          repair_port_configs:
            type: boolean
            default: true
            description: "Repair port configuration issues"
            
      cleanup_obsolete_scripts:
        description: "Detect and remove obsolete startup/shutdown scripts and batch files"
        parameters:
          dry_run:
            type: boolean
            default: true
            description: "Preview cleanup actions without actually deleting files"
          backup_before_delete:
            type: boolean
            default: true
            description: "Create backup archive of obsolete files before deletion"
          cleanup_scope:
            type: string
            default: "startup_shutdown"
            options: ["startup_shutdown", "all_batch", "custom"]
            description: "Scope of cleanup - startup/shutdown scripts, all batch files, or custom pattern"
          preserve_patterns:
            type: array
            default: ["START_COMPLETE_SYSTEM_PORT_3003.bat", "START_*_ONLY.bat", "STOP_*.bat"]
            description: "File patterns to preserve during cleanup"

  requirements-dependency-manager:
    description: "Expert requirements and dependency management specialist"
    category: "infrastructure"
    color: "green"
    entrypoint: "scripts/requirements_manager.py"
    knowledge_bases:
      - "Dependency_Resolution_Solutions.md"
      - "package.json"
      - "backend/requirements.txt"
    databases:
      - "dependency_analysis.db"
    
    tasks:
      analyze_dependencies:
        description: "Comprehensive dependency analysis and optimization"
        parameters:
          scope:
            type: string
            default: "all"
            options: ["frontend", "backend", "global", "all"]
            description: "Scope of analysis"
          include_transitive:
            type: boolean
            default: true
            description: "Include transitive dependency analysis"
            
      security_audit:
        description: "Security vulnerability assessment of all dependencies"
        parameters:
          severity_threshold:
            type: string
            default: "medium"
            options: ["low", "medium", "high", "critical"]
            description: "Minimum severity to report"

  # Development & Content Agents
  documentation-overseer:
    description: "Master technical writing specialist with adaptive documentation intelligence AND project directory management expert - maintains optimal project structure, documentation health, and provides strategic recommendations for project improvements"
    category: "development"
    color: "blue"
    entrypoint: "scripts/documentation_manager.py"
    knowledge_bases:
      - "README.md"
      - "CLAUDE.md"
      - "AGENT_ECOSYSTEM_DOCUMENTATION.md"
      - "DEVELOPMENT_GUIDELINES.md"
      - "PROJECT_STRUCTURE.md"
      - "*.md"  # All markdown files
    databases:
      - "documentation_overseer.db"
      - "documentation_intelligence.db"
      - "project_structure.db"
      - "directory_management.db"
      - "project_improvement_recommendations.db"
      - "file_dependency_tracking.db"
      - "directory_impact_analysis.db"
      - "file_relationship_matrix.db"
    
    tasks:
      audit_documentation:
        description: "Comprehensive documentation health audit with strategic project improvement recommendations"
        parameters:
          check_links:
            type: boolean
            default: true
            description: "Validate all links and references"
          check_code_examples:
            type: boolean
            default: true
            description: "Validate code examples against current implementation"
          generate_missing:
            type: boolean
            default: false
            description: "Auto-generate missing documentation drafts"
          analyze_project_structure:
            type: boolean
            default: true
            description: "Analyze and recommend improvements to project structure"
          strategic_recommendations:
            type: boolean
            default: true
            description: "Generate high-level strategic recommendations for project improvements"
            
      directory_management:
        description: "Analyze and optimize project directory structure for maximum efficiency and clarity - RECOMMENDATIONS ONLY, NO EXECUTION"
        parameters:
          restructure_analysis:
            type: boolean
            default: true
            description: "Analyze current structure and recommend optimizations"
          naming_conventions:
            type: boolean
            default: true
            description: "Review and standardize naming conventions"
          organization_optimization:
            type: boolean
            default: true
            description: "Optimize file and folder organization"
          cleanup_recommendations:
            type: boolean
            default: true
            description: "Identify files/directories for cleanup or reorganization"
          track_file_dependencies:
            type: boolean
            default: true
            description: "Map all file dependencies and relationships"
          impact_analysis:
            type: boolean
            default: true
            description: "Analyze impact of potential directory changes on other files"
          create_dependency_graph:
            type: boolean
            default: true
            description: "Create visual dependency graph showing file relationships"
          
      validate_directory_proposal:
        description: "Validate a proposed directory change for impact and safety - ANALYSIS ONLY, NO EXECUTION"
        parameters:
          source_path:
            type: string
            required: true
            description: "Current file/directory path to be moved"
          target_path:
            type: string
            required: true
            description: "Proposed new file/directory path"
          analyze_cascading_effects:
            type: boolean
            default: true
            description: "Analyze all cascading effects of the proposed move"
          generate_update_checklist:
            type: boolean
            default: true
            description: "Generate comprehensive checklist of all files that would need updates"
          risk_assessment:
            type: boolean
            default: true
            description: "Provide detailed risk assessment and safety recommendations"
          identify_breaking_changes:
            type: boolean
            default: true
            description: "Identify potential breaking changes and mitigation strategies"
          estimate_effort:
            type: boolean
            default: true
            description: "Estimate time and effort required for the change"
            
      track_file_relationships:
        description: "Comprehensive tracking and mapping of all file dependencies and relationships"
        parameters:
          scan_depth:
            type: string
            default: "comprehensive"
            options: ["surface", "standard", "comprehensive", "deep"]
            description: "Depth of dependency scanning"
          include_imports:
            type: boolean
            default: true
            description: "Track code imports and module dependencies"
          include_references:
            type: boolean
            default: true
            description: "Track documentation references and links"
          include_assets:
            type: boolean
            default: true
            description: "Track asset file dependencies (images, models, configs)"
          include_build_dependencies:
            type: boolean
            default: true
            description: "Track build system and deployment dependencies"
          create_relationship_matrix:
            type: boolean
            default: true
            description: "Create detailed relationship matrix for all tracked files"
          scalability_analysis:
            type: boolean
            default: true
            description: "Analyze structure scalability for future growth"
            
      project_improvement_strategy:
        description: "Generate comprehensive strategic recommendations for overall project improvements"
        parameters:
          architecture_analysis:
            type: boolean
            default: true
            description: "Analyze overall project architecture"
          workflow_optimization:
            type: boolean
            default: true
            description: "Recommend workflow and process improvements"
          collaboration_improvements:
            type: boolean
            default: true
            description: "Suggest improvements for team collaboration"
          maintenance_strategy:
            type: boolean
            default: true
            description: "Develop long-term maintenance strategies"
          innovation_opportunities:
            type: boolean
            default: true
            description: "Identify opportunities for innovation and enhancement"
            
      adaptive_monitoring:
        description: "Continuous monitoring and proactive documentation/structure generation"
        parameters:
          monitoring_duration:
            type: integer
            default: 24
            description: "Hours to monitor for changes"
          auto_generate_drafts:
            type: boolean
            default: true
            description: "Automatically generate documentation drafts"
          structure_monitoring:
            type: boolean
            default: true
            description: "Monitor for structural changes that need documentation"
            
      sync_with_code:
        description: "Synchronize documentation with current code state and project structure"
        parameters:
          commit_range:
            type: string
            default: "HEAD~10..HEAD"
            description: "Git commit range to analyze for changes"
          update_structure_docs:
            type: boolean
            default: true
            description: "Update documentation reflecting structural changes"

  ui-state-manager:
    description: "Expert UI State Management Architect for robust frontend state systems"
    category: "development"
    color: "cyan"
    entrypoint: "scripts/ui_state_manager.py"
    knowledge_bases:
      - "UI_STATE_MANAGEMENT_KNOWLEDGE_BASE.md"
      - "frontend/src/contexts/"
      - "frontend/src/stores/"
    databases:
      - "state_management_analysis.db"
    
    tasks:
      analyze_state_architecture:
        description: "Analyze current state management architecture and identify improvements"
        parameters:
          focus_area:
            type: string
            default: "all"
            options: ["performance", "organization", "patterns", "testing", "all"]
            description: "Primary analysis focus"
          answer_questions:
            type: boolean
            default: false
            description: "Attempt to answer pending questions in knowledge base"
            
      performance_optimization:
        description: "Optimize state management performance"
        parameters:
          target_components:
            type: array
            default: []
            description: "Specific components to optimize (empty = all)"
            
      migration_planning:
        description: "Plan migration between state management patterns"
        parameters:
          from_pattern:
            type: string
            options: ["context", "zustand", "redux", "mixed"]
            description: "Current state management pattern"
          to_pattern:
            type: string
            options: ["context", "zustand", "redux", "mixed"]
            description: "Target state management pattern"

  comfyui-workflow-orchestrator:
    description: "Expert AI system architect for dynamic ComfyUI workflow generation and optimization - creates the fastest, most efficient workflows for AI image generation with intelligent model and hardware targeting"
    category: "development"
    color: "orange"
    entrypoint: "scripts/workflow_orchestrator.py"
    knowledge_bases:
      - "COMFYUI_WORKFLOW_KNOWLEDGE_BASE.md"
      - "frontend/src/workflows/"
      - "modes/text2image/workflows/"
      - "model_inventory.md"
      - "RTX_4070_TI_SUPER_OPTIMIZATIONS.md"
    databases:
      - "workflow_orchestration.db"
      - "model_performance.db"
      - "generation_speed_optimization.db"
      - "workflow_efficiency_metrics.db"
    
    tasks:
      generate_workflow:
        description: "Generate optimized workflow for maximum speed and quality with intelligent model and hardware targeting"
        parameters:
          model_type:
            type: string
            required: true
            options: ["flux", "sdxl", "sd15", "hidream"]
            description: "Target model architecture"
          generation_mode:
            type: string
            default: "txt2img"
            options: ["txt2img", "img2img", "inpaint", "outpaint"]
            description: "Generation workflow type"
          hardware_target:
            type: string
            default: "rtx4070ti_super"
            description: "Target hardware configuration"
          speed_priority:
            type: string
            default: "balanced"
            options: ["maximum_speed", "balanced", "maximum_quality"]
            description: "Speed vs quality optimization priority"
          batch_optimization:
            type: boolean
            default: true
            description: "Optimize for batch processing"
          memory_efficiency:
            type: boolean
            default: true
            description: "Optimize memory usage for larger images/batches"
            
      validate_workflows:
        description: "Validate existing workflows and provide detailed optimization recommendations"
        parameters:
          workflow_path:
            type: string
            default: "all"
            description: "Specific workflow file or 'all' for all workflows"
          performance_benchmarking:
            type: boolean
            default: true
            description: "Run performance benchmarks on workflows"
          optimization_suggestions:
            type: boolean
            default: true
            description: "Generate specific optimization recommendations"
          compatibility_check:
            type: boolean
            default: true
            description: "Check compatibility with current models and hardware"
            
      optimize_performance:
        description: "Optimize workflow performance for maximum generation speed and efficiency"
        parameters:
          target_hardware:
            type: string
            default: "rtx4070ti_super"
            description: "Hardware configuration to optimize for"
          focus_metrics:
            type: array
            default: ["generation_speed", "memory_efficiency", "quality_preservation"]
            description: "Primary optimization metrics"
          aggressive_optimization:
            type: boolean
            default: false
            description: "Apply aggressive optimizations that may affect compatibility"
            
      workflow_intelligence:
        description: "Analyze workflow patterns and recommend intelligent improvements"
        parameters:
          pattern_analysis:
            type: boolean
            default: true
            description: "Analyze common workflow patterns for optimization"
          bottleneck_identification:
            type: boolean
            default: true
            description: "Identify specific bottlenecks in workflow execution"
          innovation_recommendations:
            type: boolean
            default: true
            description: "Recommend innovative workflow improvements"

  # Specialized Domain Agents
  image-expert:
    description: "Master visual analyst with encyclopedic knowledge of art and AI image generation - provides expert recommendations for optimal generation parameters, style techniques, and quality improvements"
    category: "domain"
    color: "magenta"
    entrypoint: "scripts/image_expert.py"
    knowledge_bases:
      - "knowledge_bases/art_styles_reference.md"
      - "knowledge_bases/generation_techniques.md"
      - "knowledge_bases/contemporary_digital_techniques.md"
      - "frontend/src/workflows/"
      - "modes/text2image/workflows/"
      - "model_inventory.md"
      - "RTX_4070_TI_SUPER_OPTIMIZATIONS.md"
    databases:
      - "image_expert.db"
      - "art_styles.db"
      - "contemporary_techniques.db"
      - "mood_analysis.db"
      - "lighting_analysis.db"
      - "color_schemes.db"
      - "generation_optimization.db"
      - "quality_enhancement_recommendations.db"
    
    tasks:
      analyze_image:
        description: "Comprehensive image analysis including style, composition, and technical aspects"
        parameters:
          image_path:
            type: string
            required: true
            description: "Path to image file for analysis"
          analysis_depth:
            type: string
            default: "comprehensive"
            options: ["basic", "standard", "comprehensive", "expert"]
            description: "Depth of analysis to perform"
          include_mood_analysis:
            type: boolean
            default: true
            description: "Include detailed mood and emotional tone analysis"
          include_lighting_analysis:
            type: boolean
            default: true
            description: "Include comprehensive lighting technique analysis"
          include_color_analysis:
            type: boolean
            default: true
            description: "Include detailed color scheme and palette analysis"
            
      detect_mood:
        description: "Specialized mood and emotional atmosphere detection"
        parameters:
          image_path:
            type: string
            required: true
            description: "Path to image file for mood analysis"
          mood_categories:
            type: array
            default: ["serene", "dramatic", "melancholic", "energetic", "mysterious"]
            description: "Specific mood categories to analyze"
          cultural_context:
            type: boolean
            default: true
            description: "Include cultural and contextual mood interpretation"
            
      analyze_lighting:
        description: "Detailed lighting technique and quality analysis"
        parameters:
          image_path:
            type: string
            required: true
            description: "Path to image file for lighting analysis"
          lighting_types:
            type: array
            default: ["natural", "artificial", "mixed"]
            description: "Types of lighting to analyze"
          technical_assessment:
            type: boolean
            default: true
            description: "Include technical lighting quality metrics"
          artistic_assessment:
            type: boolean
            default: true
            description: "Include artistic lighting technique analysis"
            
      analyze_color_scheme:
        description: "Comprehensive color palette and scheme analysis"
        parameters:
          image_path:
            type: string
            required: true
            description: "Path to image file for color analysis"
          color_theory:
            type: boolean
            default: true
            description: "Apply color theory analysis (complementary, analogous, etc.)"
          temperature_analysis:
            type: boolean
            default: true
            description: "Analyze color temperature and warmth/coolness"
          cultural_significance:
            type: boolean
            default: false
            description: "Include cultural color meaning analysis"
          palette_extraction:
            type: boolean
            default: true
            description: "Extract dominant color palette from image"
            
      optimize_generation:
        description: "Optimize generation parameters for specific art styles"
        parameters:
          target_style:
            type: string
            required: true
            description: "Target artistic style"
          model_type:
            type: string
            default: "flux"
            options: ["flux", "sdxl", "sd15"]
            description: "Model to optimize for"
          mood_target:
            type: string
            default: "auto"
            options: ["auto", "serene", "dramatic", "melancholic", "energetic", "mysterious"]
            description: "Target mood for generation"
          lighting_style:
            type: string
            default: "auto"
            options: ["auto", "golden_hour", "blue_hour", "chiaroscuro", "soft_diffused", "dramatic"]
            description: "Target lighting style"
          color_scheme:
            type: string
            default: "auto"
            options: ["auto", "complementary", "analogous", "triadic", "monochromatic", "warm", "cool"]
            description: "Target color scheme"
            
      style_transfer_analysis:
        description: "Analyze potential for style transfer and artistic adaptation"
        parameters:
          source_image:
            type: string
            required: true
            description: "Source image for style analysis"
          target_style:
            type: string
            required: true
            description: "Target artistic style for transfer"
          preserve_elements:
            type: array
            default: ["composition", "subject"]
            description: "Elements to preserve during style transfer"

  e2e-ux-quality-assurance:
    description: "Expert user experience testing specialist for comprehensive end-to-end validation"
    category: "quality"
    color: "purple"
    entrypoint: "scripts/ux_quality_assurance.py"
    knowledge_bases:
      - "frontend/src/components/"
      - "frontend/src/hooks/"
      - "user_journey_scenarios.md"  # Would be created
    databases:
      - "ux_quality_assurance.db"
      - "test_scenarios.db"
    
    tasks:
      run_user_journey_tests:
        description: "Execute comprehensive user journey validation tests"
        parameters:
          test_suite:
            type: string
            default: "comprehensive"
            options: ["smoke", "regression", "comprehensive", "performance"]
            description: "Test suite to execute"
          browser:
            type: string
            default: "all"
            options: ["chrome", "firefox", "safari", "edge", "all"]
            description: "Browser(s) to test against"
            
      accessibility_audit:
        description: "Comprehensive accessibility compliance audit"
        parameters:
          wcag_level:
            type: string
            default: "AA"
            options: ["A", "AA", "AAA"]
            description: "WCAG compliance level to test against"
            
      performance_validation:
        description: "Validate UI performance under realistic conditions"
        parameters:
          load_scenario:
            type: string
            default: "normal"
            options: ["light", "normal", "heavy", "stress"]
            description: "Load scenario to simulate"

# Task templates for common workflows
task_templates:
  ai_generation_optimization_suite:
    description: "Comprehensive AI image generation optimization across all system components"
    agents_sequence:
      - agent: "system-optimization-agent"
        task: "generation_speed_analysis"
        params:
          workflow_profiling: true
          bottleneck_identification: true
          optimization_recommendations: true
      - agent: "system-optimization-agent"
        task: "ai_environment_setup"
        params:
          tensor_core_optimization: true
          cuda_optimization: true
          model_cache_optimization: true
      - agent: "comfyui-workflow-orchestrator"
        task: "workflow_intelligence"
        params:
          pattern_analysis: true
          bottleneck_identification: true
          innovation_recommendations: true
      - agent: "dependency-orchestrator"
        task: "environment_audit"
        params:
          ai_model_focus: true
          performance_impact: true
          optimization_recommendations: true
      - agent: "documentation-overseer"
        task: "project_improvement_strategy"
        params:
          architecture_analysis: true
          workflow_optimization: true
          innovation_opportunities: true
          
  full_system_health_check:
    description: "Comprehensive system health check with AI optimization focus"
    agents_sequence:
      - agent: "system-optimization-agent"
        task: "systemaudit"
        params:
          includeharware: true
          depth: "comprehensive"
          focus_models: ["flux", "sdxl", "hidream"]
          benchmark_generation: true
      - agent: "dependency-orchestrator"
        task: "environment_audit"
        params:
          include_security: true
          ai_model_focus: true
          optimization_recommendations: true
      - agent: "system-connections-manager"
        task: "integration_health_check"
        params:
          prediction_window: 72
      - agent: "documentation-overseer"
        task: "audit_documentation"
        params:
          check_links: true
          check_code_examples: true
          analyze_project_structure: true
          strategic_recommendations: true
      - agent: "comfyui-workflow-orchestrator"
        task: "validate_workflows"
        params:
          performance_benchmarking: true
          optimization_suggestions: true
      - agent: "e2e-ux-quality-assurance"
        task: "run_user_journey_tests"
        params:
          test_suite: "smoke"
          
  pre_deployment_validation:
    description: "Complete validation before deploying changes"
    agents_sequence:
      - agent: "comfyui-workflow-orchestrator"
        task: "validate_workflows"
      - agent: "ui-state-manager"
        task: "analyze_state_architecture"
        params:
          focus_area: "performance"
      - agent: "e2e-ux-quality-assurance"
        task: "run_user_journey_tests"
        params:
          test_suite: "regression"
      - agent: "documentation-overseer"
        task: "sync_with_code"
        
  new_developer_onboarding:
    description: "Complete setup and validation for new team members"
    agents_sequence:
      - agent: "dependency-orchestrator"
        task: "setup_validation"
        params:
          create_backup: true
          test_connections: true
      - agent: "system-connections-manager"
        task: "diagnose_connections"
        params:
          component: "all"
      - agent: "documentation-overseer"
        task: "audit_documentation"
        params:
          generate_missing: true
          
  performance_optimization_suite:
    description: "Comprehensive system performance optimization for AI workloads"
    agents_sequence:
      - agent: "system-optimization-agent"
        task: "systemaudit"
        params:
          includeharware: true
          depth: "expert"
      - agent: "system-optimization-agent"
        task: "hardwareoptimization"
        params:
          targetworkload: "comfyui"
          memoryoptimization: true
      - agent: "system-optimization-agent"
        task: "optimizeperformance"
        params:
          focusarea: "all"
          aggressiveness: "moderate"
          preservestability: true
      - agent: "system-connections-manager"
        task: "optimize_performance"
        params:
          focus_area: "all"
      - agent: "comfyui-workflow-orchestrator"
        task: "optimize_performance"
        params:
          target_hardware: "rtx4070ti_super"
          
  rtx_4070_ti_super_ai_optimization:
    description: "Specialized RTX 4070 Ti SUPER optimization for maximum AI image generation performance"
    agents_sequence:
      - agent: "system-optimization-agent"
        task: "hardwareoptimization"
        params:
          targetworkload: "comfyui"
          tensor_core_optimization: true
          memory_efficiency: "aggressive"
          cuda_optimization: true
      - agent: "system-optimization-agent"
        task: "generation_speed_analysis"
        params:
          workflow_profiling: true
          bottleneck_identification: true
          model_comparison: true
          optimization_recommendations: true
      - agent: "comfyui-workflow-orchestrator"
        task: "optimize_performance"
        params:
          target_hardware: "rtx4070ti_super"
          focus_metrics: ["generation_speed", "memory_efficiency"]
          aggressive_optimization: true
      - agent: "image-expert"
        task: "optimize_generation"
        params:
          model_type: "flux"
          target_hardware: "rtx4070ti_super"
          
  project_improvement_analysis:
    description: "Comprehensive analysis and strategic recommendations for project improvements"
    agents_sequence:
      - agent: "documentation-overseer"
        task: "directory_management"
        params:
          restructure_analysis: true
          organization_optimization: true
          scalability_analysis: true
      - agent: "documentation-overseer"
        task: "project_improvement_strategy"
        params:
          architecture_analysis: true
          workflow_optimization: true
          innovation_opportunities: true
      - agent: "system-optimization-agent"
        task: "generation_speed_analysis"
        params:
          optimization_recommendations: true
      - agent: "comfyui-workflow-orchestrator"
        task: "workflow_intelligence"
        params:
          innovation_recommendations: true
      - agent: "dependency-orchestrator"
        task: "environment_audit"
        params:
          optimization_recommendations: true
          
  directory_safety_analysis:
    description: "Comprehensive directory structure analysis with safety recommendations - NO EXECUTION, ANALYSIS ONLY"
    agents_sequence:
      - agent: "documentation-overseer"
        task: "track_file_relationships"
        params:
          scan_depth: "comprehensive"
          include_imports: true
          include_references: true
          include_assets: true
          include_build_dependencies: true
          create_relationship_matrix: true
      - agent: "documentation-overseer"
        task: "directory_management"
        params:
          restructure_analysis: true
          track_file_dependencies: true
          impact_analysis: true
          create_dependency_graph: true
      - agent: "dependency-orchestrator"
        task: "environment_audit"
        params:
          include_security: true
          dependency_impact_analysis: true
          
  project_structure_optimization:
    description: "Complete project structure optimization analysis with strategic improvement recommendations"
    agents_sequence:
      - agent: "documentation-overseer"
        task: "project_improvement_strategy"
        params:
          architecture_analysis: true
          workflow_optimization: true
          collaboration_improvements: true
          maintenance_strategy: true
          innovation_opportunities: true
      - agent: "documentation-overseer"
        task: "directory_management"
        params:
          restructure_analysis: true
          naming_conventions: true
          organization_optimization: true
          cleanup_recommendations: true
          track_file_dependencies: true
          impact_analysis: true
      - agent: "system-optimization-agent"
        task: "ai_generation_optimization"
        params:
          workflow_profiling: true
          storage_optimization: true
          model_organization: true
          
  safe_directory_migration_analysis:
    description: "Comprehensive analysis for safe directory/file migrations with full impact assessment"
    agents_sequence:
      - agent: "documentation-overseer"
        task: "track_file_relationships"
        params:
          scan_depth: "deep"
          include_imports: true
          include_references: true
          include_assets: true
          include_build_dependencies: true
          create_relationship_matrix: true
      - agent: "documentation-overseer"
        task: "validate_directory_proposal"
        params:
          analyze_cascading_effects: true
          generate_update_checklist: true
          risk_assessment: true
          identify_breaking_changes: true
          estimate_effort: true
      - agent: "dependency-orchestrator"
        task: "resolve_conflicts"
        params:
          strict: true
          target_environment: "all"
          auto_fix: false
          
  comprehensive_documentation_and_structure_audit:
    description: "Complete audit of both documentation health and project structure optimization"
    agents_sequence:
      - agent: "documentation-overseer"
        task: "audit_documentation"
        params:
          check_links: true
          check_code_examples: true
          generate_missing: false
          analyze_project_structure: true
          strategic_recommendations: true
      - agent: "documentation-overseer"
        task: "directory_management"
        params:
          restructure_analysis: true
          naming_conventions: true
          organization_optimization: true
          cleanup_recommendations: true
          track_file_dependencies: true
          impact_analysis: true
          create_dependency_graph: true
      - agent: "documentation-overseer"
        task: "sync_with_code"
        params:
          commit_range: "HEAD~20..HEAD"
          update_structure_docs: true

# Reporting configuration
reporting:
  formats: ["markdown", "json", "html"]
  default_format: "markdown"
  include_metrics: true
  include_logs: true
  include_recommendations: true
  
  # Changelog generation settings
  changelog:
    auto_generate: true
    format: "markdown"
    include_diff: false
    group_by_category: true
    
  # Notification settings for reporting
  notifications:
    on_completion: true
    on_error: true
    on_critical_issues: true
    include_summary: true