#!/usr/bin/env python3
"""
Test script for model file verification functionality
"""

import asyncio
import sys
from pathlib import Path

# Add the agent scripts path
sys.path.insert(0, str(Path(__file__).parent / "agent_orchestration" / "scripts"))

from workflow_orchestrator import WorkflowOrchestratorAgent

async def test_model_verification():
    """Test the model verification functionality."""
    
    # Create agent context
    context = {
        "agent": {
            "name": "comfyui-workflow-orchestrator",
            "description": "Test agent for model verification"
        },
        "task": {
            "name": "test_model_verification",
            "description": "Test model file verification and renaming",
            "parameters": {}
        },
        "config": {
            "project_root": str(Path(__file__).parent),
            "comfyui_url": "http://localhost:8188"
        }
    }
    
    # Initialize agent
    agent = WorkflowOrchestratorAgent(context)
    
    print("🔍 Testing model file verification...")
    print("=" * 60)
    
    # Run model verification
    verification_results = await agent._verify_model_files()
    
    # Display results
    print(f"Files checked: {verification_results['files_checked']}")
    print(f"Files renamed: {verification_results['files_renamed']}")
    print(f"Missing critical files: {len(verification_results['missing_critical_files'])}")
    
    if verification_results['verification_errors']:
        print("\n⚠️ Verification Errors:")
        for error in verification_results['verification_errors']:
            print(f"  - {error}")
    
    if verification_results['missing_critical_files']:
        print("\n❌ Missing Critical Files:")
        for missing_file in verification_results['missing_critical_files']:
            print(f"  - {missing_file}")
    
    print("\n📊 Model Inventory:")
    for model_type, files in verification_results['model_inventory'].items():
        print(f"\n{model_type.upper()}:")
        for file_info in files:
            status_icon = {
                "verified": "✅",
                "renamed": "🔄", 
                "rename_failed": "❌",
                "target_exists": "⚠️"
            }.get(file_info["status"], "❓")
            
            print(f"  {status_icon} {file_info['original_name']} ({file_info['size_mb']} MB)")
            if "renamed_to" in file_info:
                print(f"    → Renamed to: {file_info['renamed_to']}")
    
    print("\n" + "=" * 60)
    print("Model verification test complete!")

if __name__ == "__main__":
    asyncio.run(test_model_verification())
