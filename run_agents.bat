@echo off
echo Running Claude Code Agents...
cd /d "G:\comfyui_Front"
echo Current directory: %CD%
echo.
echo Python version:
"G:\PORT_COMFY_front\comfycustomenv\Scripts\python.exe" --version
echo.
echo Available agents:
"G:\PORT_COMFY_front\comfycustomenv\Scripts\python.exe" deploy_agent.py --list-agents
echo.
echo Running system optimization agent:
"G:\PORT_COMFY_front\comfycustomenv\Scripts\python.exe" deploy_agent.py --agent=system-optimization-agent --task=systemaudit
pause
