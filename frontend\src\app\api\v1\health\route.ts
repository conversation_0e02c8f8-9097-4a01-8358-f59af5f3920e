import { NextResponse } from 'next/server';

// Add throttling for health check logging
let lastLogTime = 0;
const LOG_INTERVAL_MS = 30000; // Only log every 30 seconds instead of every check

interface ServiceHealth {
  status: 'connected' | 'disconnected' | 'error';
  responseTime?: number;
  error?: string;
  version?: string;
}

interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: {
    comfyui: ServiceHealth;
    ollama: ServiceHealth;
  };
  system: {
    vram: string;
    optimizations: {
      batchSize: number;
      workerThreads: number;
      memoryLimit: string;
      gpuAcceleration: boolean;
    };
  };
}

/**
 * Check ComfyUI service health
 */
async function checkComfyUIHealth(): Promise<ServiceHealth> {
  try {
    const startTime = Date.now();
    
    // Try to connect to ComfyUI API (using correct port 8188)
    const response = await fetch('http://localhost:8188/system_stats', {
      method: 'GET',
      signal: AbortSignal.timeout(5000) // 5 second timeout
    });
    
    const responseTime = Date.now() - startTime;
    
    if (response.ok) {
      const data = await response.json();
      return {
        status: 'connected',
        responseTime,
        version: data.version || 'unknown'
      };
    } else {
      return {
        status: 'error',
        responseTime,
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }
  } catch (error) {
    return {
      status: 'disconnected',
      error: error instanceof Error ? error.message : 'Connection failed'
    };
  }
}

/**
 * Check Ollama service health
 */
async function checkOllamaHealth(): Promise<ServiceHealth> {
  try {
    const startTime = Date.now();
    
    // Try to connect to Ollama API
    const response = await fetch('http://localhost:11434/api/tags', {
      method: 'GET',
      signal: AbortSignal.timeout(5000) // 5 second timeout
    });
    
    const responseTime = Date.now() - startTime;
    
    if (response.ok) {
      const data = await response.json();
      return {
        status: 'connected',
        responseTime,
        version: data.version || 'unknown'
      };
    } else {
      return {
        status: 'error',
        responseTime,
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }
  } catch (error) {
    return {
      status: 'disconnected',
      error: error instanceof Error ? error.message : 'Connection failed'
    };
  }
}

/**
 * Get system optimizations based on 16GB VRAM
 */
function getSystemOptimizations() {
  return {
    batchSize: 4, // Can handle larger batches with 16GB VRAM
    workerThreads: Math.max(4, Math.min(16, navigator.hardwareConcurrency || 8)),
    memoryLimit: '12GB', // Leave 4GB for system
    gpuAcceleration: true // Assume NVIDIA/AMD GPU with 16GB VRAM
  };
}

/**
 * GET /api/v1/health
 * Get comprehensive system health status
 */
export async function GET() {
  try {
    console.log('Performing health check...');
    
    // Check services in parallel
    const [comfyuiHealth, ollamaHealth] = await Promise.all([
      checkComfyUIHealth(),
      checkOllamaHealth()
    ]);
    
    // Determine overall system status
    let systemStatus: SystemHealth['status'] = 'healthy';
    
    if (comfyuiHealth.status === 'disconnected' && ollamaHealth.status === 'disconnected') {
      systemStatus = 'unhealthy';
    } else if (comfyuiHealth.status !== 'connected' || ollamaHealth.status !== 'connected') {
      systemStatus = 'degraded';
    }
    
    const healthData: SystemHealth = {
      status: systemStatus,
      timestamp: new Date().toISOString(),
      services: {
        comfyui: comfyuiHealth,
        ollama: ollamaHealth
      },
      system: {
        vram: '16GB',
        optimizations: getSystemOptimizations()
      }
    };
    
    // Only log health check results every 30 seconds to reduce spam
    const now = Date.now();
    if (now - lastLogTime > LOG_INTERVAL_MS) {
      console.log('Health check completed:', {
        status: systemStatus,
        comfyui: comfyuiHealth.status,
        ollama: ollamaHealth.status
      });
      lastLogTime = now;
    }
    
    return NextResponse.json(healthData);
    
  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        services: {
          comfyui: { status: 'error', error: 'Health check failed' },
          ollama: { status: 'error', error: 'Health check failed' }
        },
        system: {
          vram: '16GB',
          optimizations: getSystemOptimizations()
        },
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/v1/health/reconnect
 * Attempt to reconnect all services
 */
export async function POST() {
  try {
    console.log('Attempting service reconnection...');
    
    // Force fresh health checks
    const [comfyuiHealth, ollamaHealth] = await Promise.all([
      checkComfyUIHealth(),
      checkOllamaHealth()
    ]);
    
    // Log reconnection attempts
    const logEntry = {
      timestamp: new Date().toISOString(),
      action: 'reconnect_attempt',
      results: {
        comfyui: comfyuiHealth,
        ollama: ollamaHealth
      }
    };
    
    console.log('Reconnection attempt completed:', logEntry);
    
    // Determine overall status
    let systemStatus: SystemHealth['status'] = 'healthy';
    if (comfyuiHealth.status === 'disconnected' && ollamaHealth.status === 'disconnected') {
      systemStatus = 'unhealthy';
    } else if (comfyuiHealth.status !== 'connected' || ollamaHealth.status !== 'connected') {
      systemStatus = 'degraded';
    }
    
    return NextResponse.json({
      status: systemStatus,
      timestamp: new Date().toISOString(),
      services: {
        comfyui: comfyuiHealth,
        ollama: ollamaHealth
      },
      system: {
        vram: '16GB',
        optimizations: getSystemOptimizations()
      },
      reconnection: {
        attempted: true,
        success: systemStatus !== 'unhealthy'
      }
    });
    
  } catch (error) {
    console.error('Reconnection failed:', error);
    
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        reconnection: {
          attempted: true,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      },
      { status: 500 }
    );
  }
}
