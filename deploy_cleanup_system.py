#!/usr/bin/env python3
"""
Deploy and test the obsolete script cleanup functionality
"""

import asyncio
import sys
import os

# Add the scripts directory to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
agent_scripts_dir = os.path.join(script_dir, 'agent_orchestration', 'scripts')
sys.path.insert(0, agent_scripts_dir)

async def test_cleanup_deployment():
    """Test the cleanup functionality deployment"""
    try:
        from connections_manager import execute
        
        print("🧹 Testing Obsolete Script Cleanup System")
        print("=" * 50)
        
        # Test cleanup with dry run
        context = {
            "task_name": "cleanup_obsolete_scripts",
            "parameters": {
                "dry_run": True,
                "cleanup_scope": "startup_shutdown",
                "backup_before_delete": True
            }
        }
        
        print("Running cleanup analysis (dry run mode)...")
        result = await execute(context)
        
        print(f"✅ Success: {result.success}")
        print(f"📝 Message: {result.message}")
        
        if result.data:
            obsolete_count = len(result.data.get('obsolete_files_found', []))
            preserved_count = len(result.data.get('files_preserved', []))
            
            print(f"🗑️  Obsolete files detected: {obsolete_count}")
            print(f"🛡️  Files preserved: {preserved_count}")
            
            # Show some examples
            print("\n📋 Obsolete Files Found:")
            for i, file_info in enumerate(result.data.get('obsolete_files_found', [])[:10]):
                filename = os.path.basename(file_info['path'])
                reason = file_info['reason']
                print(f"  {i+1}. {filename} - {reason}")
            
            print("\n🔒 Preserved Files:")
            for i, preserved_path in enumerate(result.data.get('files_preserved', [])[:10]):
                filename = os.path.basename(preserved_path)
                print(f"  {i+1}. {filename}")
        
        print("\n🎯 Cleanup System Ready!")
        print("To run actual cleanup, set dry_run=False")
        
        return result.success
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_cleanup_deployment())
    sys.exit(0 if success else 1)
