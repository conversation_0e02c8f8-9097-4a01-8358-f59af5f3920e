#!/usr/bin/env python3
"""
Test script to verify workflow default settings and preferred models
"""
import asyncio
import sys
import json
from pathlib import Path

# Add the scripts directory to path
sys.path.append(str(Path(__file__).parent / "agent_orchestration" / "scripts"))

from workflow_orchestrator import WorkflowOrchestratorAgent

async def test_workflow_defaults():
    """Test workflow default settings and preferred models"""
    print("🔍 Testing workflow defaults and preferences...")
    print("=" * 60)
    
    try:
        # Test context with proper structure
        test_context = {
            "agent": {
                "name": "workflow_defaults_test"
            },
            "task": {
                "name": "test_defaults",
                "parameters": {}
            },
            "config": {
                "data_dir": "g:/comfyui_Front/data",
                "databases": [],
                "project_root": "g:/comfyui_Front"
            }
        }
        
        # Create orchestrator instance
        orchestrator = WorkflowOrchestratorAgent(test_context)
        print(f"✅ Orchestrator initialized successfully")
        
        # Test workflow defaults
        print(f"\n🎛️ Testing workflow defaults...")
        defaults = orchestrator.get_workflow_defaults()
        print(f"📊 Default batch size: {defaults['batch_size']}")
        print(f"📊 Default steps: {defaults['steps']}")
        print(f"📊 Default guidance: {defaults['guidance']}")
        print(f"📊 Default size: {defaults['width']}x{defaults['height']}")
        print(f"📊 Default scheduler: {defaults['scheduler']}")
        print(f"📊 Default sampler: {defaults['sampler']}")
        
        # Test preferred models for FLUX
        print(f"\n🎯 Testing FLUX preferred models...")
        flux_models = orchestrator.get_preferred_models("flux")
        print(f"🔸 Primary text encoder: {flux_models.get('text_encoder_primary', 'Not set')}")
        print(f"🔸 Secondary text encoder: {flux_models.get('text_encoder_secondary', 'Not set')}")
        print(f"🔸 VAE: {flux_models.get('vae', 'Not set')}")
        print(f"🔸 UNET: {flux_models.get('unet', 'Not set')}")
        
        # Test preferred models for HiDream
        print(f"\n🎯 Testing HiDream preferred models...")
        hidream_models = orchestrator.get_preferred_models("hidream")
        print(f"🔸 Primary text encoder: {hidream_models.get('text_encoder_primary', 'Not set')}")
        print(f"🔸 Secondary text encoder: {hidream_models.get('text_encoder_secondary', 'Not set')}")
        print(f"🔸 VAE: {hidream_models.get('vae', 'Not set')}")
        
        # Test workflow type detection
        print(f"\n🔍 Testing workflow type detection...")
        
        # Test FLUX workflow detection
        flux_workflow = {
            "nodes": [
                {"type": "DualCLIPLoader", "params": {"type": "flux"}},
                {"type": "UNETLoader", "params": {"unet_name": "flux1-dev.safetensors"}}
            ]
        }
        detected_type = orchestrator._detect_workflow_type(flux_workflow)
        print(f"🎯 FLUX workflow detected as: {detected_type}")
        
        # Test HiDream workflow detection
        hidream_workflow = {
            "nodes": [
                {"type": "VAELoader", "params": {"vae_name": "hidream_vae.safetensors"}},
                {"type": "CLIPLoader", "params": {"clip_name": "clip_g_hidream.safetensors"}}
            ]
        }
        detected_type = orchestrator._detect_workflow_type(hidream_workflow)
        print(f"🎯 HiDream workflow detected as: {detected_type}")
        
        # Test workflow preference application
        print(f"\n🔧 Testing workflow preference application...")
        test_workflow = {
            "nodes": [
                {
                    "id": 1,
                    "type": "DualCLIPLoader",
                    "params": {"type": "flux", "clip_name1": "old_model.safetensors", "clip_name2": "old_clip.safetensors"}
                },
                {
                    "id": 2,
                    "type": "VAELoader", 
                    "params": {"vae_name": "old_vae.safetensors"}
                },
                {
                    "id": 3,
                    "type": "EmptySD3LatentImage",
                    "params": {"width": 512, "height": 512}  # Old size, no batch_size set
                }
            ]
        }
        
        updated_workflow = orchestrator.update_workflow_with_preferences(test_workflow, "flux")
        
        print(f"📋 Updated workflow nodes:")
        for node in updated_workflow["nodes"]:
            print(f"  Node {node['id']} ({node['type']}):")
            for param, value in node["params"].items():
                print(f"    {param}: {value}")
        
        # Verify specific updates
        dual_clip_node = next(n for n in updated_workflow["nodes"] if n["type"] == "DualCLIPLoader")
        vae_node = next(n for n in updated_workflow["nodes"] if n["type"] == "VAELoader")
        latent_node = next(n for n in updated_workflow["nodes"] if n["type"] == "EmptySD3LatentImage")
        
        print(f"\n✅ Verification results:")
        print(f"🔸 Enhanced T5 model used: {dual_clip_node['params']['clip_name1'] == flux_models['text_encoder_primary']}")
        print(f"🔸 Enhanced FLUX CLIP used: {dual_clip_node['params']['clip_name2'] == flux_models['text_encoder_secondary']}")
        print(f"🔸 FLUX VAE used: {vae_node['params']['vae_name'] == flux_models['vae']}")
        print(f"🔸 Batch size set to 1: {latent_node['params']['batch_size'] == 1}")
        print(f"🔸 Default size preserved: {latent_node['params']['width']}x{latent_node['params']['height']}")
        
        print(f"\n" + "=" * 60)
        print("Workflow defaults test complete!")
        
        return {
            "success": True,
            "defaults": defaults,
            "flux_models": flux_models,
            "hidream_models": hidream_models,
            "workflow_updated": True
        }
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

if __name__ == "__main__":
    result = asyncio.run(test_workflow_defaults())
    print(f"\nTest result: {result}")
