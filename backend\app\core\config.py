from pydantic_settings import BaseSettings
from typing import List
import os

class Settings(BaseSettings):
    # API Settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "ComfyUI Custom Frontend"
    
    # ComfyUI Settings
    COMFYUI_HOST: str = "127.0.0.1"
    COMFYUI_PORT: int = 8188
    COMFYUI_API_URL: str = f"http://{COMFYUI_HOST}:{COMFYUI_PORT}"
    
    # Ollama Settings
    OLLAMA_HOST: str = "127.0.0.1"
    OLLAMA_PORT: int = 11434
    OLLAMA_API_URL: str = f"http://{OLLAMA_HOST}:{OLLAMA_PORT}"
    OLLAMA_DEFAULT_MODEL: str = "gpt-oss:latest"
    
    # Hardware Optimization Settings
    MAX_CONCURRENT_GENERATIONS: int = 2  # For RTX 4070 Ti SUPER
    VRAM_BUFFER_GB: float = 2.0  # Reserve 2GB for UI operations
    MAX_IMAGE_CACHE_SIZE_GB: float = 8.0  # Use 8GB for image caching
    
    # File Paths - Portable ComfyUI installation with external models
    COMFYUI_BASE_PATH: str = "G:/PORT_COMFY_front/ComfyUI_windows_portable/ComfyUI"
    COMFYUI_MODELS_PATH: str = "L:/ComfyUI/models"  # External model directory
    COMFYUI_OUTPUT_PATH: str = f"{COMFYUI_BASE_PATH}/output"
    TEMP_PATH: str = "C:/temp/comfyui_workspace"
    TEMP_WORKSPACE_PATH: str = "G:/comfyui_custom/temp"
    
    # Model Paths - External model directory
    CHECKPOINTS_PATH: str = f"{COMFYUI_MODELS_PATH}/checkpoints"
    LORAS_PATH: str = f"{COMFYUI_MODELS_PATH}/loras"
    CONTROLNET_PATH: str = f"{COMFYUI_MODELS_PATH}/controlnet"
    UPSCALE_MODELS_PATH: str = f"{COMFYUI_MODELS_PATH}/upscale_models"
    VAE_PATH: str = f"{COMFYUI_MODELS_PATH}/vae"
    CLIP_PATH: str = f"{COMFYUI_MODELS_PATH}/clip"
    UNET_PATH: str = f"{COMFYUI_MODELS_PATH}/unet"
    DIFFUSION_MODELS_PATH: str = f"{COMFYUI_MODELS_PATH}/diffusion_models"
    
    # CORS Settings
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3003",
        "http://127.0.0.1:3003",
    ]
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:3003",
        "http://127.0.0.1:3003",
    ]
    
    # System Monitoring
    SYSTEM_STATS_UPDATE_INTERVAL: float = 2.0  # seconds
    GPU_TEMPERATURE_WARNING: int = 80  # Celsius
    VRAM_WARNING_THRESHOLD: float = 0.9  # 90% usage
    SYSTEM_MONITOR_INTERVAL: int = 5
    SYSTEM_MONITOR_ENABLED: bool = True
    
    class Config:
        case_sensitive = True
        env_file = ".env"

settings = Settings()
