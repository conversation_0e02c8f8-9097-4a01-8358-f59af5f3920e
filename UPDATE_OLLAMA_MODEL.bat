@echo off
echo ================================================================
echo   Ollama Model Update Script - gpt-oss:latest
echo   Date: 2025-08-09
echo ================================================================
echo.

REM Check if Ollama is running
echo Checking Ollama service status...
ollama --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Ollama is not installed or not in PATH
    echo Please install Ollama from https://ollama.ai/
    pause
    exit /b 1
)

echo ✅ Ollama is available
echo.

REM Check if the new model exists
echo Checking for gpt-oss:latest model...
ollama list | findstr "gpt-oss:latest" >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ gpt-oss:latest model is already available
    echo.
    echo Current models:
    ollama list
    echo.
    echo Model update complete!
    pause
    exit /b 0
)

echo ⚠️  gpt-oss:latest model not found
echo.
echo ℹ️  To use gpt-oss:latest, please download it manually using: ollama pull gpt-oss:latest

echo.
echo ================================================================
echo   Model update process complete
echo ================================================================
pause
